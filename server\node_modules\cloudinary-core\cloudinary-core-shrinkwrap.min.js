!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.cloudinary=t():e.cloudinary=t()}(this,(function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="./src/namespace/cloudinary-core-shrinkwrap.js")}({"./node_modules/lodash/_DataView.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"DataView");e.exports=o},"./node_modules/lodash/_Hash.js":function(e,t,n){var o=n("./node_modules/lodash/_hashClear.js"),r=n("./node_modules/lodash/_hashDelete.js"),i=n("./node_modules/lodash/_hashGet.js"),s=n("./node_modules/lodash/_hashHas.js"),u=n("./node_modules/lodash/_hashSet.js");function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}a.prototype.clear=o,a.prototype.delete=r,a.prototype.get=i,a.prototype.has=s,a.prototype.set=u,e.exports=a},"./node_modules/lodash/_ListCache.js":function(e,t,n){var o=n("./node_modules/lodash/_listCacheClear.js"),r=n("./node_modules/lodash/_listCacheDelete.js"),i=n("./node_modules/lodash/_listCacheGet.js"),s=n("./node_modules/lodash/_listCacheHas.js"),u=n("./node_modules/lodash/_listCacheSet.js");function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}a.prototype.clear=o,a.prototype.delete=r,a.prototype.get=i,a.prototype.has=s,a.prototype.set=u,e.exports=a},"./node_modules/lodash/_Map.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"Map");e.exports=o},"./node_modules/lodash/_MapCache.js":function(e,t,n){var o=n("./node_modules/lodash/_mapCacheClear.js"),r=n("./node_modules/lodash/_mapCacheDelete.js"),i=n("./node_modules/lodash/_mapCacheGet.js"),s=n("./node_modules/lodash/_mapCacheHas.js"),u=n("./node_modules/lodash/_mapCacheSet.js");function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}a.prototype.clear=o,a.prototype.delete=r,a.prototype.get=i,a.prototype.has=s,a.prototype.set=u,e.exports=a},"./node_modules/lodash/_Promise.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"Promise");e.exports=o},"./node_modules/lodash/_Set.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"Set");e.exports=o},"./node_modules/lodash/_SetCache.js":function(e,t,n){var o=n("./node_modules/lodash/_MapCache.js"),r=n("./node_modules/lodash/_setCacheAdd.js"),i=n("./node_modules/lodash/_setCacheHas.js");function s(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new o;++t<n;)this.add(e[t])}s.prototype.add=s.prototype.push=r,s.prototype.has=i,e.exports=s},"./node_modules/lodash/_Stack.js":function(e,t,n){var o=n("./node_modules/lodash/_ListCache.js"),r=n("./node_modules/lodash/_stackClear.js"),i=n("./node_modules/lodash/_stackDelete.js"),s=n("./node_modules/lodash/_stackGet.js"),u=n("./node_modules/lodash/_stackHas.js"),a=n("./node_modules/lodash/_stackSet.js");function l(e){var t=this.__data__=new o(e);this.size=t.size}l.prototype.clear=r,l.prototype.delete=i,l.prototype.get=s,l.prototype.has=u,l.prototype.set=a,e.exports=l},"./node_modules/lodash/_Symbol.js":function(e,t,n){var o=n("./node_modules/lodash/_root.js").Symbol;e.exports=o},"./node_modules/lodash/_Uint8Array.js":function(e,t,n){var o=n("./node_modules/lodash/_root.js").Uint8Array;e.exports=o},"./node_modules/lodash/_WeakMap.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"WeakMap");e.exports=o},"./node_modules/lodash/_apply.js":function(e,t){e.exports=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}},"./node_modules/lodash/_arrayEach.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}},"./node_modules/lodash/_arrayFilter.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length,r=0,i=[];++n<o;){var s=e[n];t(s,n,e)&&(i[r++]=s)}return i}},"./node_modules/lodash/_arrayIncludes.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js");e.exports=function(e,t){return!!(null==e?0:e.length)&&o(e,t,0)>-1}},"./node_modules/lodash/_arrayIncludesWith.js":function(e,t){e.exports=function(e,t,n){for(var o=-1,r=null==e?0:e.length;++o<r;)if(n(t,e[o]))return!0;return!1}},"./node_modules/lodash/_arrayLikeKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_baseTimes.js"),r=n("./node_modules/lodash/isArguments.js"),i=n("./node_modules/lodash/isArray.js"),s=n("./node_modules/lodash/isBuffer.js"),u=n("./node_modules/lodash/_isIndex.js"),a=n("./node_modules/lodash/isTypedArray.js"),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=i(e),c=!n&&r(e),d=!n&&!c&&s(e),f=!n&&!c&&!d&&a(e),h=n||c||d||f,p=h?o(e.length,String):[],y=p.length;for(var m in e)!t&&!l.call(e,m)||h&&("length"==m||d&&("offset"==m||"parent"==m)||f&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||u(m,y))||p.push(m);return p}},"./node_modules/lodash/_arrayMap.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length,r=Array(o);++n<o;)r[n]=t(e[n],n,e);return r}},"./node_modules/lodash/_arrayPush.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=t.length,r=e.length;++n<o;)e[r+n]=t[n];return e}},"./node_modules/lodash/_asciiToArray.js":function(e,t){e.exports=function(e){return e.split("")}},"./node_modules/lodash/_assignMergeValue.js":function(e,t,n){var o=n("./node_modules/lodash/_baseAssignValue.js"),r=n("./node_modules/lodash/eq.js");e.exports=function(e,t,n){(void 0!==n&&!r(e[t],n)||void 0===n&&!(t in e))&&o(e,t,n)}},"./node_modules/lodash/_assignValue.js":function(e,t,n){var o=n("./node_modules/lodash/_baseAssignValue.js"),r=n("./node_modules/lodash/eq.js"),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,n){var s=e[t];i.call(e,t)&&r(s,n)&&(void 0!==n||t in e)||o(e,t,n)}},"./node_modules/lodash/_assocIndexOf.js":function(e,t,n){var o=n("./node_modules/lodash/eq.js");e.exports=function(e,t){for(var n=e.length;n--;)if(o(e[n][0],t))return n;return-1}},"./node_modules/lodash/_baseAssign.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/keys.js");e.exports=function(e,t){return e&&o(t,r(t),e)}},"./node_modules/lodash/_baseAssignIn.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/keysIn.js");e.exports=function(e,t){return e&&o(t,r(t),e)}},"./node_modules/lodash/_baseAssignValue.js":function(e,t,n){var o=n("./node_modules/lodash/_defineProperty.js");e.exports=function(e,t,n){"__proto__"==t&&o?o(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},"./node_modules/lodash/_baseClone.js":function(e,t,n){var o=n("./node_modules/lodash/_Stack.js"),r=n("./node_modules/lodash/_arrayEach.js"),i=n("./node_modules/lodash/_assignValue.js"),s=n("./node_modules/lodash/_baseAssign.js"),u=n("./node_modules/lodash/_baseAssignIn.js"),a=n("./node_modules/lodash/_cloneBuffer.js"),l=n("./node_modules/lodash/_copyArray.js"),c=n("./node_modules/lodash/_copySymbols.js"),d=n("./node_modules/lodash/_copySymbolsIn.js"),f=n("./node_modules/lodash/_getAllKeys.js"),h=n("./node_modules/lodash/_getAllKeysIn.js"),p=n("./node_modules/lodash/_getTag.js"),y=n("./node_modules/lodash/_initCloneArray.js"),m=n("./node_modules/lodash/_initCloneByTag.js"),_=n("./node_modules/lodash/_initCloneObject.js"),v=n("./node_modules/lodash/isArray.js"),b=n("./node_modules/lodash/isBuffer.js"),g=n("./node_modules/lodash/isMap.js"),j=n("./node_modules/lodash/isObject.js"),w=n("./node_modules/lodash/isSet.js"),A=n("./node_modules/lodash/keys.js"),O=n("./node_modules/lodash/keysIn.js"),D={};D["[object Arguments]"]=D["[object Array]"]=D["[object ArrayBuffer]"]=D["[object DataView]"]=D["[object Boolean]"]=D["[object Date]"]=D["[object Float32Array]"]=D["[object Float64Array]"]=D["[object Int8Array]"]=D["[object Int16Array]"]=D["[object Int32Array]"]=D["[object Map]"]=D["[object Number]"]=D["[object Object]"]=D["[object RegExp]"]=D["[object Set]"]=D["[object String]"]=D["[object Symbol]"]=D["[object Uint8Array]"]=D["[object Uint8ClampedArray]"]=D["[object Uint16Array]"]=D["[object Uint32Array]"]=!0,D["[object Error]"]=D["[object Function]"]=D["[object WeakMap]"]=!1,e.exports=function E(e,t,n,B,C,S){var k,x=1&t,F=2&t,P=4&t;if(n&&(k=C?n(e,B,C,S):n(e)),void 0!==k)return k;if(!j(e))return e;var T=v(e);if(T){if(k=y(e),!x)return l(e,k)}else{var I=p(e),R="[object Function]"==I||"[object GeneratorFunction]"==I;if(b(e))return a(e,x);if("[object Object]"==I||"[object Arguments]"==I||R&&!C){if(k=F||R?{}:_(e),!x)return F?d(e,u(k,e)):c(e,s(k,e))}else{if(!D[I])return C?e:{};k=m(e,I,x)}}S||(S=new o);var L=S.get(e);if(L)return L;S.set(e,k),w(e)?e.forEach((function(o){k.add(E(o,t,n,o,e,S))})):g(e)&&e.forEach((function(o,r){k.set(r,E(o,t,n,r,e,S))}));var z=T?void 0:(P?F?h:f:F?O:A)(e);return r(z||e,(function(o,r){z&&(o=e[r=o]),i(k,r,E(o,t,n,r,e,S))})),k}},"./node_modules/lodash/_baseCreate.js":function(e,t,n){var o=n("./node_modules/lodash/isObject.js"),r=Object.create,i=function(){function e(){}return function(t){if(!o(t))return{};if(r)return r(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();e.exports=i},"./node_modules/lodash/_baseDifference.js":function(e,t,n){var o=n("./node_modules/lodash/_SetCache.js"),r=n("./node_modules/lodash/_arrayIncludes.js"),i=n("./node_modules/lodash/_arrayIncludesWith.js"),s=n("./node_modules/lodash/_arrayMap.js"),u=n("./node_modules/lodash/_baseUnary.js"),a=n("./node_modules/lodash/_cacheHas.js");e.exports=function(e,t,n,l){var c=-1,d=r,f=!0,h=e.length,p=[],y=t.length;if(!h)return p;n&&(t=s(t,u(n))),l?(d=i,f=!1):t.length>=200&&(d=a,f=!1,t=new o(t));e:for(;++c<h;){var m=e[c],_=null==n?m:n(m);if(m=l||0!==m?m:0,f&&_==_){for(var v=y;v--;)if(t[v]===_)continue e;p.push(m)}else d(t,_,l)||p.push(m)}return p}},"./node_modules/lodash/_baseFindIndex.js":function(e,t){e.exports=function(e,t,n,o){for(var r=e.length,i=n+(o?1:-1);o?i--:++i<r;)if(t(e[i],i,e))return i;return-1}},"./node_modules/lodash/_baseFlatten.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayPush.js"),r=n("./node_modules/lodash/_isFlattenable.js");e.exports=function i(e,t,n,s,u){var a=-1,l=e.length;for(n||(n=r),u||(u=[]);++a<l;){var c=e[a];t>0&&n(c)?t>1?i(c,t-1,n,s,u):o(u,c):s||(u[u.length]=c)}return u}},"./node_modules/lodash/_baseFor.js":function(e,t,n){var o=n("./node_modules/lodash/_createBaseFor.js")();e.exports=o},"./node_modules/lodash/_baseFunctions.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayFilter.js"),r=n("./node_modules/lodash/isFunction.js");e.exports=function(e,t){return o(t,(function(t){return r(e[t])}))}},"./node_modules/lodash/_baseGetAllKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayPush.js"),r=n("./node_modules/lodash/isArray.js");e.exports=function(e,t,n){var i=t(e);return r(e)?i:o(i,n(e))}},"./node_modules/lodash/_baseGetTag.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=n("./node_modules/lodash/_getRawTag.js"),i=n("./node_modules/lodash/_objectToString.js"),s=o?o.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":s&&s in Object(e)?r(e):i(e)}},"./node_modules/lodash/_baseIndexOf.js":function(e,t,n){var o=n("./node_modules/lodash/_baseFindIndex.js"),r=n("./node_modules/lodash/_baseIsNaN.js"),i=n("./node_modules/lodash/_strictIndexOf.js");e.exports=function(e,t,n){return t==t?i(e,t,n):o(e,r,n)}},"./node_modules/lodash/_baseIsArguments.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&"[object Arguments]"==o(e)}},"./node_modules/lodash/_baseIsMap.js":function(e,t,n){var o=n("./node_modules/lodash/_getTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&"[object Map]"==o(e)}},"./node_modules/lodash/_baseIsNaN.js":function(e,t){e.exports=function(e){return e!=e}},"./node_modules/lodash/_baseIsNative.js":function(e,t,n){var o=n("./node_modules/lodash/isFunction.js"),r=n("./node_modules/lodash/_isMasked.js"),i=n("./node_modules/lodash/isObject.js"),s=n("./node_modules/lodash/_toSource.js"),u=/^\[object .+?Constructor\]$/,a=Function.prototype,l=Object.prototype,c=a.toString,d=l.hasOwnProperty,f=RegExp("^"+c.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||r(e))&&(o(e)?f:u).test(s(e))}},"./node_modules/lodash/_baseIsSet.js":function(e,t,n){var o=n("./node_modules/lodash/_getTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&"[object Set]"==o(e)}},"./node_modules/lodash/_baseIsTypedArray.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isLength.js"),i=n("./node_modules/lodash/isObjectLike.js"),s={};s["[object Float32Array]"]=s["[object Float64Array]"]=s["[object Int8Array]"]=s["[object Int16Array]"]=s["[object Int32Array]"]=s["[object Uint8Array]"]=s["[object Uint8ClampedArray]"]=s["[object Uint16Array]"]=s["[object Uint32Array]"]=!0,s["[object Arguments]"]=s["[object Array]"]=s["[object ArrayBuffer]"]=s["[object Boolean]"]=s["[object DataView]"]=s["[object Date]"]=s["[object Error]"]=s["[object Function]"]=s["[object Map]"]=s["[object Number]"]=s["[object Object]"]=s["[object RegExp]"]=s["[object Set]"]=s["[object String]"]=s["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&r(e.length)&&!!s[o(e)]}},"./node_modules/lodash/_baseKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_isPrototype.js"),r=n("./node_modules/lodash/_nativeKeys.js"),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return r(e);var t=[];for(var n in Object(e))i.call(e,n)&&"constructor"!=n&&t.push(n);return t}},"./node_modules/lodash/_baseKeysIn.js":function(e,t,n){var o=n("./node_modules/lodash/isObject.js"),r=n("./node_modules/lodash/_isPrototype.js"),i=n("./node_modules/lodash/_nativeKeysIn.js"),s=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return i(e);var t=r(e),n=[];for(var u in e)("constructor"!=u||!t&&s.call(e,u))&&n.push(u);return n}},"./node_modules/lodash/_baseMerge.js":function(e,t,n){var o=n("./node_modules/lodash/_Stack.js"),r=n("./node_modules/lodash/_assignMergeValue.js"),i=n("./node_modules/lodash/_baseFor.js"),s=n("./node_modules/lodash/_baseMergeDeep.js"),u=n("./node_modules/lodash/isObject.js"),a=n("./node_modules/lodash/keysIn.js"),l=n("./node_modules/lodash/_safeGet.js");e.exports=function c(e,t,n,d,f){e!==t&&i(t,(function(i,a){if(f||(f=new o),u(i))s(e,t,a,n,c,d,f);else{var h=d?d(l(e,a),i,a+"",e,t,f):void 0;void 0===h&&(h=i),r(e,a,h)}}),a)}},"./node_modules/lodash/_baseMergeDeep.js":function(e,t,n){var o=n("./node_modules/lodash/_assignMergeValue.js"),r=n("./node_modules/lodash/_cloneBuffer.js"),i=n("./node_modules/lodash/_cloneTypedArray.js"),s=n("./node_modules/lodash/_copyArray.js"),u=n("./node_modules/lodash/_initCloneObject.js"),a=n("./node_modules/lodash/isArguments.js"),l=n("./node_modules/lodash/isArray.js"),c=n("./node_modules/lodash/isArrayLikeObject.js"),d=n("./node_modules/lodash/isBuffer.js"),f=n("./node_modules/lodash/isFunction.js"),h=n("./node_modules/lodash/isObject.js"),p=n("./node_modules/lodash/isPlainObject.js"),y=n("./node_modules/lodash/isTypedArray.js"),m=n("./node_modules/lodash/_safeGet.js"),_=n("./node_modules/lodash/toPlainObject.js");e.exports=function(e,t,n,v,b,g,j){var w=m(e,n),A=m(t,n),O=j.get(A);if(O)o(e,n,O);else{var D=g?g(w,A,n+"",e,t,j):void 0,E=void 0===D;if(E){var B=l(A),C=!B&&d(A),S=!B&&!C&&y(A);D=A,B||C||S?l(w)?D=w:c(w)?D=s(w):C?(E=!1,D=r(A,!0)):S?(E=!1,D=i(A,!0)):D=[]:p(A)||a(A)?(D=w,a(w)?D=_(w):h(w)&&!f(w)||(D=u(A))):E=!1}E&&(j.set(A,D),b(D,A,v,g,j),j.delete(A)),o(e,n,D)}}},"./node_modules/lodash/_baseRest.js":function(e,t,n){var o=n("./node_modules/lodash/identity.js"),r=n("./node_modules/lodash/_overRest.js"),i=n("./node_modules/lodash/_setToString.js");e.exports=function(e,t){return i(r(e,t,o),e+"")}},"./node_modules/lodash/_baseSetToString.js":function(e,t,n){var o=n("./node_modules/lodash/constant.js"),r=n("./node_modules/lodash/_defineProperty.js"),i=n("./node_modules/lodash/identity.js"),s=r?function(e,t){return r(e,"toString",{configurable:!0,enumerable:!1,value:o(t),writable:!0})}:i;e.exports=s},"./node_modules/lodash/_baseSlice.js":function(e,t){e.exports=function(e,t,n){var o=-1,r=e.length;t<0&&(t=-t>r?0:r+t),(n=n>r?r:n)<0&&(n+=r),r=t>n?0:n-t>>>0,t>>>=0;for(var i=Array(r);++o<r;)i[o]=e[o+t];return i}},"./node_modules/lodash/_baseTimes.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}},"./node_modules/lodash/_baseToString.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=n("./node_modules/lodash/_arrayMap.js"),i=n("./node_modules/lodash/isArray.js"),s=n("./node_modules/lodash/isSymbol.js"),u=o?o.prototype:void 0,a=u?u.toString:void 0;e.exports=function l(e){if("string"==typeof e)return e;if(i(e))return r(e,l)+"";if(s(e))return a?a.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},"./node_modules/lodash/_baseTrim.js":function(e,t,n){var o=n("./node_modules/lodash/_trimmedEndIndex.js"),r=/^\s+/;e.exports=function(e){return e?e.slice(0,o(e)+1).replace(r,""):e}},"./node_modules/lodash/_baseUnary.js":function(e,t){e.exports=function(e){return function(t){return e(t)}}},"./node_modules/lodash/_baseValues.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayMap.js");e.exports=function(e,t){return o(t,(function(t){return e[t]}))}},"./node_modules/lodash/_cacheHas.js":function(e,t){e.exports=function(e,t){return e.has(t)}},"./node_modules/lodash/_castSlice.js":function(e,t,n){var o=n("./node_modules/lodash/_baseSlice.js");e.exports=function(e,t,n){var r=e.length;return n=void 0===n?r:n,!t&&n>=r?e:o(e,t,n)}},"./node_modules/lodash/_charsEndIndex.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js");e.exports=function(e,t){for(var n=e.length;n--&&o(t,e[n],0)>-1;);return n}},"./node_modules/lodash/_charsStartIndex.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js");e.exports=function(e,t){for(var n=-1,r=e.length;++n<r&&o(t,e[n],0)>-1;);return n}},"./node_modules/lodash/_cloneArrayBuffer.js":function(e,t,n){var o=n("./node_modules/lodash/_Uint8Array.js");e.exports=function(e){var t=new e.constructor(e.byteLength);return new o(t).set(new o(e)),t}},"./node_modules/lodash/_cloneBuffer.js":function(e,t,n){(function(e){var o=n("./node_modules/lodash/_root.js"),r=t&&!t.nodeType&&t,i=r&&"object"==typeof e&&e&&!e.nodeType&&e,s=i&&i.exports===r?o.Buffer:void 0,u=s?s.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,o=u?u(n):new e.constructor(n);return e.copy(o),o}}).call(this,n("./node_modules/webpack/buildin/module.js")(e))},"./node_modules/lodash/_cloneDataView.js":function(e,t,n){var o=n("./node_modules/lodash/_cloneArrayBuffer.js");e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}},"./node_modules/lodash/_cloneRegExp.js":function(e,t){var n=/\w*$/;e.exports=function(e){var t=new e.constructor(e.source,n.exec(e));return t.lastIndex=e.lastIndex,t}},"./node_modules/lodash/_cloneSymbol.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=o?o.prototype:void 0,i=r?r.valueOf:void 0;e.exports=function(e){return i?Object(i.call(e)):{}}},"./node_modules/lodash/_cloneTypedArray.js":function(e,t,n){var o=n("./node_modules/lodash/_cloneArrayBuffer.js");e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}},"./node_modules/lodash/_copyArray.js":function(e,t){e.exports=function(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t}},"./node_modules/lodash/_copyObject.js":function(e,t,n){var o=n("./node_modules/lodash/_assignValue.js"),r=n("./node_modules/lodash/_baseAssignValue.js");e.exports=function(e,t,n,i){var s=!n;n||(n={});for(var u=-1,a=t.length;++u<a;){var l=t[u],c=i?i(n[l],e[l],l,n,e):void 0;void 0===c&&(c=e[l]),s?r(n,l,c):o(n,l,c)}return n}},"./node_modules/lodash/_copySymbols.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/_getSymbols.js");e.exports=function(e,t){return o(e,r(e),t)}},"./node_modules/lodash/_copySymbolsIn.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/_getSymbolsIn.js");e.exports=function(e,t){return o(e,r(e),t)}},"./node_modules/lodash/_coreJsData.js":function(e,t,n){var o=n("./node_modules/lodash/_root.js")["__core-js_shared__"];e.exports=o},"./node_modules/lodash/_createAssigner.js":function(e,t,n){var o=n("./node_modules/lodash/_baseRest.js"),r=n("./node_modules/lodash/_isIterateeCall.js");e.exports=function(e){return o((function(t,n){var o=-1,i=n.length,s=i>1?n[i-1]:void 0,u=i>2?n[2]:void 0;for(s=e.length>3&&"function"==typeof s?(i--,s):void 0,u&&r(n[0],n[1],u)&&(s=i<3?void 0:s,i=1),t=Object(t);++o<i;){var a=n[o];a&&e(t,a,o,s)}return t}))}},"./node_modules/lodash/_createBaseFor.js":function(e,t){e.exports=function(e){return function(t,n,o){for(var r=-1,i=Object(t),s=o(t),u=s.length;u--;){var a=s[e?u:++r];if(!1===n(i[a],a,i))break}return t}}},"./node_modules/lodash/_defineProperty.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js"),r=function(){try{var e=o(Object,"defineProperty");return e({},"",{}),e}catch(t){}}();e.exports=r},"./node_modules/lodash/_freeGlobal.js":function(e,t,n){(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e.exports=n}).call(this,n("./node_modules/webpack/buildin/global.js"))},"./node_modules/lodash/_getAllKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetAllKeys.js"),r=n("./node_modules/lodash/_getSymbols.js"),i=n("./node_modules/lodash/keys.js");e.exports=function(e){return o(e,i,r)}},"./node_modules/lodash/_getAllKeysIn.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetAllKeys.js"),r=n("./node_modules/lodash/_getSymbolsIn.js"),i=n("./node_modules/lodash/keysIn.js");e.exports=function(e){return o(e,i,r)}},"./node_modules/lodash/_getMapData.js":function(e,t,n){var o=n("./node_modules/lodash/_isKeyable.js");e.exports=function(e,t){var n=e.__data__;return o(t)?n["string"==typeof t?"string":"hash"]:n.map}},"./node_modules/lodash/_getNative.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsNative.js"),r=n("./node_modules/lodash/_getValue.js");e.exports=function(e,t){var n=r(e,t);return o(n)?n:void 0}},"./node_modules/lodash/_getPrototype.js":function(e,t,n){var o=n("./node_modules/lodash/_overArg.js")(Object.getPrototypeOf,Object);e.exports=o},"./node_modules/lodash/_getRawTag.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=Object.prototype,i=r.hasOwnProperty,s=r.toString,u=o?o.toStringTag:void 0;e.exports=function(e){var t=i.call(e,u),n=e[u];try{e[u]=void 0;var o=!0}catch(a){}var r=s.call(e);return o&&(t?e[u]=n:delete e[u]),r}},"./node_modules/lodash/_getSymbols.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayFilter.js"),r=n("./node_modules/lodash/stubArray.js"),i=Object.prototype.propertyIsEnumerable,s=Object.getOwnPropertySymbols,u=s?function(e){return null==e?[]:(e=Object(e),o(s(e),(function(t){return i.call(e,t)})))}:r;e.exports=u},"./node_modules/lodash/_getSymbolsIn.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayPush.js"),r=n("./node_modules/lodash/_getPrototype.js"),i=n("./node_modules/lodash/_getSymbols.js"),s=n("./node_modules/lodash/stubArray.js"),u=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)o(t,i(e)),e=r(e);return t}:s;e.exports=u},"./node_modules/lodash/_getTag.js":function(e,t,n){var o=n("./node_modules/lodash/_DataView.js"),r=n("./node_modules/lodash/_Map.js"),i=n("./node_modules/lodash/_Promise.js"),s=n("./node_modules/lodash/_Set.js"),u=n("./node_modules/lodash/_WeakMap.js"),a=n("./node_modules/lodash/_baseGetTag.js"),l=n("./node_modules/lodash/_toSource.js"),c=l(o),d=l(r),f=l(i),h=l(s),p=l(u),y=a;(o&&"[object DataView]"!=y(new o(new ArrayBuffer(1)))||r&&"[object Map]"!=y(new r)||i&&"[object Promise]"!=y(i.resolve())||s&&"[object Set]"!=y(new s)||u&&"[object WeakMap]"!=y(new u))&&(y=function(e){var t=a(e),n="[object Object]"==t?e.constructor:void 0,o=n?l(n):"";if(o)switch(o){case c:return"[object DataView]";case d:return"[object Map]";case f:return"[object Promise]";case h:return"[object Set]";case p:return"[object WeakMap]"}return t}),e.exports=y},"./node_modules/lodash/_getValue.js":function(e,t){e.exports=function(e,t){return null==e?void 0:e[t]}},"./node_modules/lodash/_hasUnicode.js":function(e,t){var n=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return n.test(e)}},"./node_modules/lodash/_hashClear.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js");e.exports=function(){this.__data__=o?o(null):{},this.size=0}},"./node_modules/lodash/_hashDelete.js":function(e,t){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},"./node_modules/lodash/_hashGet.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js"),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(o){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return r.call(t,e)?t[e]:void 0}},"./node_modules/lodash/_hashHas.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js"),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return o?void 0!==t[e]:r.call(t,e)}},"./node_modules/lodash/_hashSet.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js");e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=o&&void 0===t?"__lodash_hash_undefined__":t,this}},"./node_modules/lodash/_initCloneArray.js":function(e,t){var n=Object.prototype.hasOwnProperty;e.exports=function(e){var t=e.length,o=new e.constructor(t);return t&&"string"==typeof e[0]&&n.call(e,"index")&&(o.index=e.index,o.input=e.input),o}},"./node_modules/lodash/_initCloneByTag.js":function(e,t,n){var o=n("./node_modules/lodash/_cloneArrayBuffer.js"),r=n("./node_modules/lodash/_cloneDataView.js"),i=n("./node_modules/lodash/_cloneRegExp.js"),s=n("./node_modules/lodash/_cloneSymbol.js"),u=n("./node_modules/lodash/_cloneTypedArray.js");e.exports=function(e,t,n){var a=e.constructor;switch(t){case"[object ArrayBuffer]":return o(e);case"[object Boolean]":case"[object Date]":return new a(+e);case"[object DataView]":return r(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return u(e,n);case"[object Map]":return new a;case"[object Number]":case"[object String]":return new a(e);case"[object RegExp]":return i(e);case"[object Set]":return new a;case"[object Symbol]":return s(e)}}},"./node_modules/lodash/_initCloneObject.js":function(e,t,n){var o=n("./node_modules/lodash/_baseCreate.js"),r=n("./node_modules/lodash/_getPrototype.js"),i=n("./node_modules/lodash/_isPrototype.js");e.exports=function(e){return"function"!=typeof e.constructor||i(e)?{}:o(r(e))}},"./node_modules/lodash/_isFlattenable.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=n("./node_modules/lodash/isArguments.js"),i=n("./node_modules/lodash/isArray.js"),s=o?o.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||r(e)||!!(s&&e&&e[s])}},"./node_modules/lodash/_isIndex.js":function(e,t){var n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var o=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==o||"symbol"!=o&&n.test(e))&&e>-1&&e%1==0&&e<t}},"./node_modules/lodash/_isIterateeCall.js":function(e,t,n){var o=n("./node_modules/lodash/eq.js"),r=n("./node_modules/lodash/isArrayLike.js"),i=n("./node_modules/lodash/_isIndex.js"),s=n("./node_modules/lodash/isObject.js");e.exports=function(e,t,n){if(!s(n))return!1;var u=typeof t;return!!("number"==u?r(n)&&i(t,n.length):"string"==u&&t in n)&&o(n[t],e)}},"./node_modules/lodash/_isKeyable.js":function(e,t){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},"./node_modules/lodash/_isMasked.js":function(e,t,n){var o,r=n("./node_modules/lodash/_coreJsData.js"),i=(o=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||""))?"Symbol(src)_1."+o:"";e.exports=function(e){return!!i&&i in e}},"./node_modules/lodash/_isPrototype.js":function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},"./node_modules/lodash/_listCacheClear.js":function(e,t){e.exports=function(){this.__data__=[],this.size=0}},"./node_modules/lodash/_listCacheDelete.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js"),r=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=o(t,e);return!(n<0)&&(n==t.length-1?t.pop():r.call(t,n,1),--this.size,!0)}},"./node_modules/lodash/_listCacheGet.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js");e.exports=function(e){var t=this.__data__,n=o(t,e);return n<0?void 0:t[n][1]}},"./node_modules/lodash/_listCacheHas.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js");e.exports=function(e){return o(this.__data__,e)>-1}},"./node_modules/lodash/_listCacheSet.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js");e.exports=function(e,t){var n=this.__data__,r=o(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}},"./node_modules/lodash/_mapCacheClear.js":function(e,t,n){var o=n("./node_modules/lodash/_Hash.js"),r=n("./node_modules/lodash/_ListCache.js"),i=n("./node_modules/lodash/_Map.js");e.exports=function(){this.size=0,this.__data__={hash:new o,map:new(i||r),string:new o}}},"./node_modules/lodash/_mapCacheDelete.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e){var t=o(this,e).delete(e);return this.size-=t?1:0,t}},"./node_modules/lodash/_mapCacheGet.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e){return o(this,e).get(e)}},"./node_modules/lodash/_mapCacheHas.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e){return o(this,e).has(e)}},"./node_modules/lodash/_mapCacheSet.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e,t){var n=o(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}},"./node_modules/lodash/_nativeCreate.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(Object,"create");e.exports=o},"./node_modules/lodash/_nativeKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_overArg.js")(Object.keys,Object);e.exports=o},"./node_modules/lodash/_nativeKeysIn.js":function(e,t){e.exports=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}},"./node_modules/lodash/_nodeUtil.js":function(e,t,n){(function(e){var o=n("./node_modules/lodash/_freeGlobal.js"),r=t&&!t.nodeType&&t,i=r&&"object"==typeof e&&e&&!e.nodeType&&e,s=i&&i.exports===r&&o.process,u=function(){try{var e=i&&i.require&&i.require("util").types;return e||s&&s.binding&&s.binding("util")}catch(t){}}();e.exports=u}).call(this,n("./node_modules/webpack/buildin/module.js")(e))},"./node_modules/lodash/_objectToString.js":function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},"./node_modules/lodash/_overArg.js":function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},"./node_modules/lodash/_overRest.js":function(e,t,n){var o=n("./node_modules/lodash/_apply.js"),r=Math.max;e.exports=function(e,t,n){return t=r(void 0===t?e.length-1:t,0),function(){for(var i=arguments,s=-1,u=r(i.length-t,0),a=Array(u);++s<u;)a[s]=i[t+s];s=-1;for(var l=Array(t+1);++s<t;)l[s]=i[s];return l[t]=n(a),o(e,this,l)}}},"./node_modules/lodash/_root.js":function(e,t,n){var o=n("./node_modules/lodash/_freeGlobal.js"),r="object"==typeof self&&self&&self.Object===Object&&self,i=o||r||Function("return this")();e.exports=i},"./node_modules/lodash/_safeGet.js":function(e,t){e.exports=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}},"./node_modules/lodash/_setCacheAdd.js":function(e,t){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},"./node_modules/lodash/_setCacheHas.js":function(e,t){e.exports=function(e){return this.__data__.has(e)}},"./node_modules/lodash/_setToString.js":function(e,t,n){var o=n("./node_modules/lodash/_baseSetToString.js"),r=n("./node_modules/lodash/_shortOut.js")(o);e.exports=r},"./node_modules/lodash/_shortOut.js":function(e,t){var n=Date.now;e.exports=function(e){var t=0,o=0;return function(){var r=n(),i=16-(r-o);if(o=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}},"./node_modules/lodash/_stackClear.js":function(e,t,n){var o=n("./node_modules/lodash/_ListCache.js");e.exports=function(){this.__data__=new o,this.size=0}},"./node_modules/lodash/_stackDelete.js":function(e,t){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},"./node_modules/lodash/_stackGet.js":function(e,t){e.exports=function(e){return this.__data__.get(e)}},"./node_modules/lodash/_stackHas.js":function(e,t){e.exports=function(e){return this.__data__.has(e)}},"./node_modules/lodash/_stackSet.js":function(e,t,n){var o=n("./node_modules/lodash/_ListCache.js"),r=n("./node_modules/lodash/_Map.js"),i=n("./node_modules/lodash/_MapCache.js");e.exports=function(e,t){var n=this.__data__;if(n instanceof o){var s=n.__data__;if(!r||s.length<199)return s.push([e,t]),this.size=++n.size,this;n=this.__data__=new i(s)}return n.set(e,t),this.size=n.size,this}},"./node_modules/lodash/_strictIndexOf.js":function(e,t){e.exports=function(e,t,n){for(var o=n-1,r=e.length;++o<r;)if(e[o]===t)return o;return-1}},"./node_modules/lodash/_stringToArray.js":function(e,t,n){var o=n("./node_modules/lodash/_asciiToArray.js"),r=n("./node_modules/lodash/_hasUnicode.js"),i=n("./node_modules/lodash/_unicodeToArray.js");e.exports=function(e){return r(e)?i(e):o(e)}},"./node_modules/lodash/_toSource.js":function(e,t){var n=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return n.call(e)}catch(t){}try{return e+""}catch(t){}}return""}},"./node_modules/lodash/_trimmedEndIndex.js":function(e,t){var n=/\s/;e.exports=function(e){for(var t=e.length;t--&&n.test(e.charAt(t)););return t}},"./node_modules/lodash/_unicodeToArray.js":function(e,t){var n="[\\ud800-\\udfff]",o="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",r="\\ud83c[\\udffb-\\udfff]",i="[^\\ud800-\\udfff]",s="(?:\\ud83c[\\udde6-\\uddff]){2}",u="[\\ud800-\\udbff][\\udc00-\\udfff]",a="(?:"+o+"|"+r+")"+"?",l="[\\ufe0e\\ufe0f]?"+a+("(?:\\u200d(?:"+[i,s,u].join("|")+")[\\ufe0e\\ufe0f]?"+a+")*"),c="(?:"+[i+o+"?",o,s,u,n].join("|")+")",d=RegExp(r+"(?="+r+")|"+c+l,"g");e.exports=function(e){return e.match(d)||[]}},"./node_modules/lodash/assign.js":function(e,t,n){var o=n("./node_modules/lodash/_assignValue.js"),r=n("./node_modules/lodash/_copyObject.js"),i=n("./node_modules/lodash/_createAssigner.js"),s=n("./node_modules/lodash/isArrayLike.js"),u=n("./node_modules/lodash/_isPrototype.js"),a=n("./node_modules/lodash/keys.js"),l=Object.prototype.hasOwnProperty,c=i((function(e,t){if(u(t)||s(t))r(t,a(t),e);else for(var n in t)l.call(t,n)&&o(e,n,t[n])}));e.exports=c},"./node_modules/lodash/cloneDeep.js":function(e,t,n){var o=n("./node_modules/lodash/_baseClone.js");e.exports=function(e){return o(e,5)}},"./node_modules/lodash/compact.js":function(e,t){e.exports=function(e){for(var t=-1,n=null==e?0:e.length,o=0,r=[];++t<n;){var i=e[t];i&&(r[o++]=i)}return r}},"./node_modules/lodash/constant.js":function(e,t){e.exports=function(e){return function(){return e}}},"./node_modules/lodash/difference.js":function(e,t,n){var o=n("./node_modules/lodash/_baseDifference.js"),r=n("./node_modules/lodash/_baseFlatten.js"),i=n("./node_modules/lodash/_baseRest.js"),s=n("./node_modules/lodash/isArrayLikeObject.js"),u=i((function(e,t){return s(e)?o(e,r(t,1,s,!0)):[]}));e.exports=u},"./node_modules/lodash/eq.js":function(e,t){e.exports=function(e,t){return e===t||e!=e&&t!=t}},"./node_modules/lodash/functions.js":function(e,t,n){var o=n("./node_modules/lodash/_baseFunctions.js"),r=n("./node_modules/lodash/keys.js");e.exports=function(e){return null==e?[]:o(e,r(e))}},"./node_modules/lodash/identity.js":function(e,t){e.exports=function(e){return e}},"./node_modules/lodash/includes.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js"),r=n("./node_modules/lodash/isArrayLike.js"),i=n("./node_modules/lodash/isString.js"),s=n("./node_modules/lodash/toInteger.js"),u=n("./node_modules/lodash/values.js"),a=Math.max;e.exports=function(e,t,n,l){e=r(e)?e:u(e),n=n&&!l?s(n):0;var c=e.length;return n<0&&(n=a(c+n,0)),i(e)?n<=c&&e.indexOf(t,n)>-1:!!c&&o(e,t,n)>-1}},"./node_modules/lodash/isArguments.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsArguments.js"),r=n("./node_modules/lodash/isObjectLike.js"),i=Object.prototype,s=i.hasOwnProperty,u=i.propertyIsEnumerable,a=o(function(){return arguments}())?o:function(e){return r(e)&&s.call(e,"callee")&&!u.call(e,"callee")};e.exports=a},"./node_modules/lodash/isArray.js":function(e,t){var n=Array.isArray;e.exports=n},"./node_modules/lodash/isArrayLike.js":function(e,t,n){var o=n("./node_modules/lodash/isFunction.js"),r=n("./node_modules/lodash/isLength.js");e.exports=function(e){return null!=e&&r(e.length)&&!o(e)}},"./node_modules/lodash/isArrayLikeObject.js":function(e,t,n){var o=n("./node_modules/lodash/isArrayLike.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&o(e)}},"./node_modules/lodash/isBuffer.js":function(e,t,n){(function(e){var o=n("./node_modules/lodash/_root.js"),r=n("./node_modules/lodash/stubFalse.js"),i=t&&!t.nodeType&&t,s=i&&"object"==typeof e&&e&&!e.nodeType&&e,u=s&&s.exports===i?o.Buffer:void 0,a=(u?u.isBuffer:void 0)||r;e.exports=a}).call(this,n("./node_modules/webpack/buildin/module.js")(e))},"./node_modules/lodash/isElement.js":function(e,t,n){var o=n("./node_modules/lodash/isObjectLike.js"),r=n("./node_modules/lodash/isPlainObject.js");e.exports=function(e){return o(e)&&1===e.nodeType&&!r(e)}},"./node_modules/lodash/isFunction.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isObject.js");e.exports=function(e){if(!r(e))return!1;var t=o(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},"./node_modules/lodash/isLength.js":function(e,t){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},"./node_modules/lodash/isMap.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsMap.js"),r=n("./node_modules/lodash/_baseUnary.js"),i=n("./node_modules/lodash/_nodeUtil.js"),s=i&&i.isMap,u=s?r(s):o;e.exports=u},"./node_modules/lodash/isObject.js":function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},"./node_modules/lodash/isObjectLike.js":function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},"./node_modules/lodash/isPlainObject.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/_getPrototype.js"),i=n("./node_modules/lodash/isObjectLike.js"),s=Function.prototype,u=Object.prototype,a=s.toString,l=u.hasOwnProperty,c=a.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=o(e))return!1;var t=r(e);if(null===t)return!0;var n=l.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&a.call(n)==c}},"./node_modules/lodash/isSet.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsSet.js"),r=n("./node_modules/lodash/_baseUnary.js"),i=n("./node_modules/lodash/_nodeUtil.js"),s=i&&i.isSet,u=s?r(s):o;e.exports=u},"./node_modules/lodash/isString.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isArray.js"),i=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return"string"==typeof e||!r(e)&&i(e)&&"[object String]"==o(e)}},"./node_modules/lodash/isSymbol.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return"symbol"==typeof e||r(e)&&"[object Symbol]"==o(e)}},"./node_modules/lodash/isTypedArray.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsTypedArray.js"),r=n("./node_modules/lodash/_baseUnary.js"),i=n("./node_modules/lodash/_nodeUtil.js"),s=i&&i.isTypedArray,u=s?r(s):o;e.exports=u},"./node_modules/lodash/keys.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayLikeKeys.js"),r=n("./node_modules/lodash/_baseKeys.js"),i=n("./node_modules/lodash/isArrayLike.js");e.exports=function(e){return i(e)?o(e):r(e)}},"./node_modules/lodash/keysIn.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayLikeKeys.js"),r=n("./node_modules/lodash/_baseKeysIn.js"),i=n("./node_modules/lodash/isArrayLike.js");e.exports=function(e){return i(e)?o(e,!0):r(e)}},"./node_modules/lodash/merge.js":function(e,t,n){var o=n("./node_modules/lodash/_baseMerge.js"),r=n("./node_modules/lodash/_createAssigner.js")((function(e,t,n){o(e,t,n)}));e.exports=r},"./node_modules/lodash/stubArray.js":function(e,t){e.exports=function(){return[]}},"./node_modules/lodash/stubFalse.js":function(e,t){e.exports=function(){return!1}},"./node_modules/lodash/toFinite.js":function(e,t,n){var o=n("./node_modules/lodash/toNumber.js");e.exports=function(e){return e?(e=o(e))===1/0||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}},"./node_modules/lodash/toInteger.js":function(e,t,n){var o=n("./node_modules/lodash/toFinite.js");e.exports=function(e){var t=o(e),n=t%1;return t==t?n?t-n:t:0}},"./node_modules/lodash/toNumber.js":function(e,t,n){var o=n("./node_modules/lodash/_baseTrim.js"),r=n("./node_modules/lodash/isObject.js"),i=n("./node_modules/lodash/isSymbol.js"),s=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,a=/^0o[0-7]+$/i,l=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return NaN;if(r(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=r(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=o(e);var n=u.test(e);return n||a.test(e)?l(e.slice(2),n?2:8):s.test(e)?NaN:+e}},"./node_modules/lodash/toPlainObject.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/keysIn.js");e.exports=function(e){return o(e,r(e))}},"./node_modules/lodash/toString.js":function(e,t,n){var o=n("./node_modules/lodash/_baseToString.js");e.exports=function(e){return null==e?"":o(e)}},"./node_modules/lodash/trim.js":function(e,t,n){var o=n("./node_modules/lodash/_baseToString.js"),r=n("./node_modules/lodash/_baseTrim.js"),i=n("./node_modules/lodash/_castSlice.js"),s=n("./node_modules/lodash/_charsEndIndex.js"),u=n("./node_modules/lodash/_charsStartIndex.js"),a=n("./node_modules/lodash/_stringToArray.js"),l=n("./node_modules/lodash/toString.js");e.exports=function(e,t,n){if((e=l(e))&&(n||void 0===t))return r(e);if(!e||!(t=o(t)))return e;var c=a(e),d=a(t),f=u(c,d),h=s(c,d)+1;return i(c,f,h).join("")}},"./node_modules/lodash/values.js":function(e,t,n){var o=n("./node_modules/lodash/_baseValues.js"),r=n("./node_modules/lodash/keys.js");e.exports=function(e){return null==e?[]:o(e,r(e))}},"./node_modules/webpack/buildin/global.js":function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(o){"object"==typeof window&&(n=window)}e.exports=n},"./node_modules/webpack/buildin/module.js":function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},"./src/namespace/cloudinary-core-shrinkwrap.js":function(e,t,n){"use strict";n.r(t),n.d(t,"ClientHintsMetaTag",(function(){return Qo})),n.d(t,"Cloudinary",(function(){return gr})),n.d(t,"Condition",(function(){return pt})),n.d(t,"Configuration",(function(){return wt})),n.d(t,"Expression",(function(){return ut})),n.d(t,"crc32",(function(){return s})),n.d(t,"FetchLayer",(function(){return Kt})),n.d(t,"HtmlTag",(function(){return Tn})),n.d(t,"ImageTag",(function(){return lo})),n.d(t,"Layer",(function(){return Et})),n.d(t,"PictureTag",(function(){return ko})),n.d(t,"SubtitlesLayer",(function(){return Nt})),n.d(t,"TextLayer",(function(){return Pt})),n.d(t,"Transformation",(function(){return Cn})),n.d(t,"utf8_encode",(function(){return i})),n.d(t,"Util",(function(){return r})),n.d(t,"VideoTag",(function(){return Ho}));var o={};n.r(o),n.d(o,"VERSION",(function(){return Y})),n.d(o,"CF_SHARED_CDN",(function(){return Q})),n.d(o,"OLD_AKAMAI_SHARED_CDN",(function(){return Z})),n.d(o,"AKAMAI_SHARED_CDN",(function(){return X})),n.d(o,"SHARED_CDN",(function(){return J})),n.d(o,"DEFAULT_TIMEOUT_MS",(function(){return ee})),n.d(o,"DEFAULT_POSTER_OPTIONS",(function(){return te})),n.d(o,"DEFAULT_VIDEO_SOURCE_TYPES",(function(){return ne})),n.d(o,"SEO_TYPES",(function(){return oe})),n.d(o,"DEFAULT_IMAGE_PARAMS",(function(){return re})),n.d(o,"DEFAULT_VIDEO_PARAMS",(function(){return ie})),n.d(o,"DEFAULT_VIDEO_SOURCES",(function(){return se})),n.d(o,"DEFAULT_EXTERNAL_LIBRARIES",(function(){return ue})),n.d(o,"PLACEHOLDER_IMAGE_MODES",(function(){return ae})),n.d(o,"ACCESSIBILITY_MODES",(function(){return le})),n.d(o,"URL_KEYS",(function(){return ce}));var r={};n.r(r),n.d(r,"getSDKAnalyticsSignature",(function(){return p})),n.d(r,"getAnalyticsOptions",(function(){return m})),n.d(r,"assign",(function(){return v.a})),n.d(r,"cloneDeep",(function(){return g.a})),n.d(r,"compact",(function(){return w.a})),n.d(r,"difference",(function(){return O.a})),n.d(r,"functions",(function(){return E.a})),n.d(r,"identity",(function(){return C.a})),n.d(r,"includes",(function(){return k.a})),n.d(r,"isArray",(function(){return F.a})),n.d(r,"isPlainObject",(function(){return T.a})),n.d(r,"isString",(function(){return R.a})),n.d(r,"merge",(function(){return z.a})),n.d(r,"contains",(function(){return k.a})),n.d(r,"isIntersectionObserverSupported",(function(){return G})),n.d(r,"isNativeLazyLoadSupported",(function(){return K})),n.d(r,"detectIntersection",(function(){return q})),n.d(r,"omit",(function(){return fe})),n.d(r,"allStrings",(function(){return pe})),n.d(r,"without",(function(){return ye})),n.d(r,"isNumberLike",(function(){return me})),n.d(r,"smartEscape",(function(){return _e})),n.d(r,"defaults",(function(){return ve})),n.d(r,"objectProto",(function(){return be})),n.d(r,"objToString",(function(){return ge})),n.d(r,"isObject",(function(){return je})),n.d(r,"funcTag",(function(){return we})),n.d(r,"reWords",(function(){return Oe})),n.d(r,"camelCase",(function(){return De})),n.d(r,"snakeCase",(function(){return Ee})),n.d(r,"convertKeys",(function(){return Be})),n.d(r,"withCamelCaseKeys",(function(){return Ce})),n.d(r,"withSnakeCaseKeys",(function(){return Se})),n.d(r,"base64Encode",(function(){return ke})),n.d(r,"base64EncodeURL",(function(){return xe})),n.d(r,"extractUrlParams",(function(){return Fe})),n.d(r,"patchFetchFormat",(function(){return Pe})),n.d(r,"optionConsume",(function(){return Te})),n.d(r,"isEmpty",(function(){return Ie})),n.d(r,"isAndroid",(function(){return Le})),n.d(r,"isEdge",(function(){return ze})),n.d(r,"isChrome",(function(){return Me})),n.d(r,"isSafari",(function(){return Ne})),n.d(r,"isElement",(function(){return N.a})),n.d(r,"isFunction",(function(){return U.a})),n.d(r,"trim",(function(){return W.a})),n.d(r,"getData",(function(){return Ve})),n.d(r,"setData",(function(){return Ue})),n.d(r,"getAttribute",(function(){return He})),n.d(r,"setAttribute",(function(){return We})),n.d(r,"removeAttribute",(function(){return $e})),n.d(r,"setAttributes",(function(){return Ge})),n.d(r,"hasClass",(function(){return Ke})),n.d(r,"addClass",(function(){return qe})),n.d(r,"getStyles",(function(){return Ye})),n.d(r,"cssExpand",(function(){return Qe})),n.d(r,"domStyle",(function(){return Ze})),n.d(r,"curCSS",(function(){return Xe})),n.d(r,"cssValue",(function(){return Je})),n.d(r,"augmentWidthOrHeight",(function(){return et})),n.d(r,"getWidthOrHeight",(function(){return nt})),n.d(r,"width",(function(){return ot}));var i=function(e){var t,n,o,r,i,s,u,a;if(null==e)return"";for(a="",i=void 0,o=void 0,0,i=o=0,u=(s=e+"").length,r=0;r<u;)n=null,(t=s.charCodeAt(r))<128?o++:n=t>127&&t<2048?String.fromCharCode(t>>6|192,63&t|128):String.fromCharCode(t>>12|224,t>>6&63|128,63&t|128),null!==n&&(o>i&&(a+=s.slice(i,o)),a+=n,i=o=r+1),r++;return o>i&&(a+=s.slice(i,u)),a};var s=function(e){var t,n,o,r;for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t=0,0,r=0,t^=-1,n=0,o=(e=i(e)).length;n<o;)r=255&(t^e.charCodeAt(n)),t=t>>>8^"0x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substr(9*r,8),n++;return(t^=-1)<0&&(t+=4294967296),t};function u(e,t,n){return t>>=0,n=String(void 0!==n?n:" "),e.length>t?String(e):((t-=e.length)>n.length&&(n+=function(e,t){var n="";for(;t>0;)n+=e,t--;return n}(n,t/n.length)),n.slice(0,t)+String(e))}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var l,c=0,d={};(l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",function(e){if(Array.isArray(e))return a(e)}(l)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(l)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}(l)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).forEach((function(e){var t=c.toString(2);t=u(t,6,"0"),d[t]=e,c++}));var f=d;function h(e){var t="",n=6*e.split(".").length,o=function(e){if(e.split(".").length<2)throw new Error("invalid semVer, must have at least two segments");return e.split(".").reverse().map((function(e){return u(e,2,"0")})).join(".")}(e),r=parseInt(o.split(".").join("")).toString(2);if((r=u(r,n,"0")).length%6!=0)throw"Version must be smaller than 43.21.26)";return r.match(/.{1,6}/g).forEach((function(e){t+=f[e]})),t}function p(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{var t=y(e.techVersion),n=h(e.sdkSemver),o=h(t),r=e.feature,i=e.sdkCode,s="A";return"".concat(s).concat(i).concat(n).concat(o).concat(r)}catch(u){return"E"}}function y(e){var t=e.split(".");return"".concat(t[0],".").concat(t[1])}function m(e){var t={sdkSemver:e.sdkSemver,techVersion:e.techVersion,sdkCode:e.sdkCode,feature:"0"};return e.urlAnalytics?(e.accessibility&&(t.feature="D"),"lazy"===e.loading&&(t.feature="C"),e.responsive&&(t.feature="A"),e.placeholder&&(t.feature="B"),t):{}}var _=n("./node_modules/lodash/assign.js"),v=n.n(_),b=n("./node_modules/lodash/cloneDeep.js"),g=n.n(b),j=n("./node_modules/lodash/compact.js"),w=n.n(j),A=n("./node_modules/lodash/difference.js"),O=n.n(A),D=n("./node_modules/lodash/functions.js"),E=n.n(D),B=n("./node_modules/lodash/identity.js"),C=n.n(B),S=n("./node_modules/lodash/includes.js"),k=n.n(S),x=n("./node_modules/lodash/isArray.js"),F=n.n(x),P=n("./node_modules/lodash/isPlainObject.js"),T=n.n(P),I=n("./node_modules/lodash/isString.js"),R=n.n(I),L=n("./node_modules/lodash/merge.js"),z=n.n(L),M=n("./node_modules/lodash/isElement.js"),N=n.n(M),V=n("./node_modules/lodash/isFunction.js"),U=n.n(V),H=n("./node_modules/lodash/trim.js"),W=n.n(H);function $(e){return($="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function G(){return"object"===("undefined"==typeof window?"undefined":$(window))&&window.IntersectionObserver}function K(){return"object"===("undefined"==typeof HTMLImageElement?"undefined":$(HTMLImageElement))&&HTMLImageElement.prototype.loading}function q(e,t){try{if(K()||!G())return void t();var n=new IntersectionObserver((function(e){e.forEach((function(e){e.isIntersecting&&(t(),n.unobserve(e.target))}))}),{threshold:[0,.01]});n.observe(e)}catch(o){t()}}var Y="2.5.0",Q="d3jpl91pxevbkh.cloudfront.net",Z="cloudinary-a.akamaihd.net",X="res.cloudinary.com",J=X,ee=1e4,te={format:"jpg",resource_type:"video"},ne=["webm","mp4","ogv"],oe={"image/upload":"images","image/private":"private_images","image/authenticated":"authenticated_images","raw/upload":"files","video/upload":"videos"},re={resource_type:"image",transformation:[],type:"upload"},ie={fallback_content:"",resource_type:"video",source_transformation:{},source_types:ne,transformation:[],type:"upload"},se=[{type:"mp4",codecs:"hev1",transformations:{video_codec:"h265"}},{type:"webm",codecs:"vp9",transformations:{video_codec:"vp9"}},{type:"mp4",transformations:{video_codec:"auto"}},{type:"webm",transformations:{video_codec:"auto"}}],ue={seeThru:"https://unpkg.com/seethru@4/dist/seeThru.min.js"},ae={blur:[{effect:"blur:2000",quality:1,fetch_format:"auto"}],pixelate:[{effect:"pixelate",quality:1,fetch_format:"auto"}],"predominant-color-pixel":[{width:"iw_div_2",aspect_ratio:1,crop:"pad",background:"auto"},{crop:"crop",width:1,height:1,gravity:"north_east"},{fetch_format:"auto",quality:"auto"}],"predominant-color":[{variables:[["$currWidth","w"],["$currHeight","h"]]},{width:"iw_div_2",aspect_ratio:1,crop:"pad",background:"auto"},{crop:"crop",width:10,height:10,gravity:"north_east"},{width:"$currWidth",height:"$currHeight",crop:"fill"},{fetch_format:"auto",quality:"auto"}],vectorize:[{effect:"vectorize:3:0.1",fetch_format:"svg"}]},le={darkmode:"tint:75:black",brightmode:"tint:50:white",monochrome:"grayscale",colorblind:"assist_colorblind"},ce=["accessibility","api_secret","auth_token","cdn_subdomain","cloud_name","cname","format","placeholder","private_cdn","resource_type","secure","secure_cdn_subdomain","secure_distribution","shorten","sign_url","signature","ssl_detected","type","url_suffix","use_root_path","version"];function de(e){return(de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fe(e,t){e=e||{};var n=Object.keys(e).filter((function(e){return!k()(t,e)})),o={};return n.forEach((function(t){return o[t]=e[t]})),o}var he,pe=function(e){return e.length&&e.every(R.a)},ye=function(e,t){return e.filter((function(e){return e!==t}))},me=function(e){return null!=e&&!isNaN(parseFloat(e))},_e=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:/([^a-zA-Z0-9_.\-\/:]+)/g;return e.replace(t,(function(e){return e.split("").map((function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})).join("")}))},ve=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return n.reduce((function(e,t){var n,o;for(n in t)o=t[n],void 0===e[n]&&(e[n]=o);return e}),e)},be=Object.prototype,ge=be.toString,je=function(e){var t;return t=de(e),!!e&&("object"===t||"function"===t)},we="[object Function]",Ae=function(e){return je(e)&&ge.call(e)===we},Oe=RegExp("[A-Z]+(?=[A-Z][a-z]+)|[A-Z]?[a-z]+|[A-Z]+|[0-9]+","g"),De=function(e){var t=e.match(Oe);return(t=t.map((function(e){return e.charAt(0).toLocaleUpperCase()+e.slice(1).toLocaleLowerCase()})))[0]=t[0].toLocaleLowerCase(),t.join("")},Ee=function(e){var t=e.match(Oe);return(t=t.map((function(e){return e.toLocaleLowerCase()}))).join("_")},Be=function(e,t){var n,o;for(var r in n={},e)o=e[r],t&&(r=t(r)),Ie(r)||(n[r]=o);return n},Ce=function(e){return Be(e,De)},Se=function(e){return Be(e,Ee)},ke="undefined"!=typeof btoa&&Ae(btoa)?btoa:"undefined"!=typeof Buffer&&Ae(Buffer)?function(e){return e instanceof Buffer||(e=new Buffer.from(String(e),"binary")),e.toString("base64")}:function(e){throw new Error("No base64 encoding function found")},xe=function(e){try{e=decodeURI(e)}finally{e=encodeURI(e)}return ke(e)};function Fe(e){return ce.reduce((function(t,n){return null!=e[n]&&(t[n]=e[n]),t}),{})}function Pe(e){null==e&&(e={}),"fetch"===e.type&&null==e.fetch_format&&(e.fetch_format=Te(e,"format"))}function Te(e,t,n){var o=e[t];return delete e[t],null!=o?o:n}function Ie(e){if(null==e)return!0;if("number"==typeof e.length)return 0===e.length;if("number"==typeof e.size)return 0===e.size;if("object"==de(e)){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}return!0}function Re(){return navigator&&navigator.userAgent||""}function Le(){var e=Re();return/Android/i.test(e)}function ze(){var e=Re();return/Edg/i.test(e)}function Me(){var e=Re();return!ze()&&(/Chrome/i.test(e)||/CriOS/i.test(e))}function Ne(){var e=Re();return/Safari/i.test(e)&&!Me()&&!Le()&&!ze()}var Ve=function(e,t){switch(!1){case!(null==e):return;case!U()(e.getAttribute):return e.getAttribute("data-".concat(t));case!U()(e.getAttr):return e.getAttr("data-".concat(t));case!U()(e.data):return e.data(t);case!(U()("undefined"!=typeof jQuery&&jQuery.fn&&jQuery.fn.data)&&N()(e)):return jQuery(e).data(t)}},Ue=function(e,t,n){switch(!1){case!(null==e):return;case!U()(e.setAttribute):return e.setAttribute("data-".concat(t),n);case!U()(e.setAttr):return e.setAttr("data-".concat(t),n);case!U()(e.data):return e.data(t,n);case!(U()("undefined"!=typeof jQuery&&jQuery.fn&&jQuery.fn.data)&&N()(e)):return jQuery(e).data(t,n)}},He=function(e,t){switch(!1){case!(null==e):return;case!U()(e.getAttribute):return e.getAttribute(t);case!U()(e.attr):return e.attr(t);case!U()(e.getAttr):return e.getAttr(t)}},We=function(e,t,n){switch(!1){case!(null==e):return;case!U()(e.setAttribute):return e.setAttribute(t,n);case!U()(e.attr):return e.attr(t,n);case!U()(e.setAttr):return e.setAttr(t,n)}},$e=function(e,t){switch(!1){case!(null==e):return;case!U()(e.removeAttribute):return e.removeAttribute(t);default:return We(e,void 0)}},Ge=function(e,t){var n,o,r;for(n in o=[],t)null!=(r=t[n])?o.push(We(e,n,r)):o.push($e(e,n));return o},Ke=function(e,t){if(N()(e))return e.className.match(new RegExp("\\b".concat(t,"\\b")))},qe=function(e,t){if(!e.className.match(new RegExp("\\b".concat(t,"\\b"))))return e.className=W()("".concat(e.className," ").concat(t))},Ye=function(e){return e.ownerDocument.defaultView.opener?e.ownerDocument.defaultView.getComputedStyle(e,null):window.getComputedStyle(e,null)},Qe=["Top","Right","Bottom","Left"];he=function(e,t){var n,o;return n=9===e.nodeType?e.documentElement:e,e===(o=t&&t.parentNode)||!(!o||1!==o.nodeType||!n.contains(o))};var Ze=function(e,t){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style)return e.style[t]},Xe=function(e,t,n){var o,r,i,s,u,a;return s=/^margin/,a=void 0,r=void 0,o=void 0,i=void 0,u=e.style,(n=n||Ye(e))&&(i=n.getPropertyValue(t)||n[t]),n&&(""!==i||he(e.ownerDocument,e)||(i=Ze(e,t)),tt.test(i)&&s.test(t)&&(a=u.width,r=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=i,i=n.width,u.width=a,u.minWidth=r,u.maxWidth=o)),void 0!==i?i+"":i},Je=function(e,t,n,o){var r;return r=Xe(e,t,o),n?parseFloat(r):r},et=function(e,t,n,o,r){var i,s,u,a,l;if(n===(o?"border":"content"))return 0;for(l=0,i=0,s=(a="width"===t?["Right","Left"]:["Top","Bottom"]).length;i<s;i++)u=a[i],"margin"===n&&(l+=Je(e,n+u,!0,r)),o?("content"===n&&(l-=Je(e,"padding".concat(u),!0,r)),"margin"!==n&&(l-=Je(e,"border".concat(u,"Width"),!0,r))):(l+=Je(e,"padding".concat(u),!0,r),"padding"!==n&&(l+=Je(e,"border".concat(u,"Width"),!0,r)));return l},tt=new RegExp("^("+/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source+")(?!px)[a-z%]+$","i"),nt=function(e,t,n){var o,r,i,s;if(s=!0,i="width"===t?e.offsetWidth:e.offsetHeight,r=Ye(e),o="border-box"===Je(e,"boxSizing",!1,r),i<=0||null==i){if(((i=Xe(e,t,r))<0||null==i)&&(i=e.style[t]),tt.test(i))return i;s=o&&i===e.style[t],i=parseFloat(i)||0}return i+et(e,t,n||(o?"border":"content"),s,r)},ot=function(e){return nt(e,"width","content")};function rt(e){return(rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function it(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,st(o.key),o)}}function st(e){var t=function(e,t){if("object"!=rt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=rt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rt(t)?t:t+""}var Expression=function(){function Expression(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Expression),this.expressions=[],null!=e&&this.expressions.push(Expression.normalize(e))}return e=Expression,n=[{key:"new",value:function(e){return new this(e)}},{key:"normalize",value:function(e){if(null==e)return e;e=String(e);var t=new RegExp("((\\|\\||>=|<=|&&|!=|>|=|<|/|-|\\+|\\*|\\^)(?=[ _]))","g");e=e.replace(t,(function(e){return Expression.OPERATORS[e]}));var n="("+Object.keys(Expression.PREDEFINED_VARS).map((function(e){return":".concat(e,"|").concat(e)})).join("|")+")",o=new RegExp("".concat("(\\$_*[^_ ]+)","|").concat(n),"g");return(e=e.replace(o,(function(e){return Expression.PREDEFINED_VARS[e]||e}))).replace(/[ _]+/g,"_")}},{key:"variable",value:function(e,t){return new this(e).value(t)}},{key:"width",value:function(){return new this("width")}},{key:"height",value:function(){return new this("height")}},{key:"initialWidth",value:function(){return new this("initialWidth")}},{key:"initialHeight",value:function(){return new this("initialHeight")}},{key:"aspectRatio",value:function(){return new this("aspectRatio")}},{key:"initialAspectRatio",value:function(){return new this("initialAspectRatio")}},{key:"pageCount",value:function(){return new this("pageCount")}},{key:"faceCount",value:function(){return new this("faceCount")}},{key:"currentPage",value:function(){return new this("currentPage")}},{key:"tags",value:function(){return new this("tags")}},{key:"pageX",value:function(){return new this("pageX")}},{key:"pageY",value:function(){return new this("pageY")}}],(t=[{key:"serialize",value:function(){return Expression.normalize(this.expressions.join("_"))}},{key:"toString",value:function(){return this.serialize()}},{key:"getParent",value:function(){return this.parent}},{key:"setParent",value:function(e){return this.parent=e,this}},{key:"predicate",value:function(e,t,n){return null!=Expression.OPERATORS[t]&&(t=Expression.OPERATORS[t]),this.expressions.push("".concat(e,"_").concat(t,"_").concat(n)),this}},{key:"and",value:function(){return this.expressions.push("and"),this}},{key:"or",value:function(){return this.expressions.push("or"),this}},{key:"then",value:function(){return this.getParent().if(this.toString())}},{key:"height",value:function(e,t){return this.predicate("h",e,t)}},{key:"width",value:function(e,t){return this.predicate("w",e,t)}},{key:"aspectRatio",value:function(e,t){return this.predicate("ar",e,t)}},{key:"pageCount",value:function(e,t){return this.predicate("pc",e,t)}},{key:"faceCount",value:function(e,t){return this.predicate("fc",e,t)}},{key:"value",value:function(e){return this.expressions.push(e),this}}])&&it(e.prototype,t),n&&it(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();Expression.OPERATORS={"=":"eq","!=":"ne","<":"lt",">":"gt","<=":"lte",">=":"gte","&&":"and","||":"or","*":"mul","/":"div","+":"add","-":"sub","^":"pow"},Expression.PREDEFINED_VARS={aspect_ratio:"ar",aspectRatio:"ar",current_page:"cp",currentPage:"cp",duration:"du",face_count:"fc",faceCount:"fc",height:"h",initial_aspect_ratio:"iar",initial_duration:"idu",initial_height:"ih",initial_width:"iw",initialAspectRatio:"iar",initialDuration:"idu",initialHeight:"ih",initialWidth:"iw",page_count:"pc",page_x:"px",page_y:"py",pageCount:"pc",pageX:"px",pageY:"py",tags:"tags",width:"w"},Expression.BOUNDRY="[ _]+";var ut=Expression;function at(e){return(at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lt(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ct(o.key),o)}}function ct(e){var t=function(e,t){if("object"!=at(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=at(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==at(t)?t:t+""}function dt(e,t,n){return t=ft(t),function(e,t){if(t&&("object"===at(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],ft(e).constructor):t.apply(e,n))}function ft(e){return(ft=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ht(e,t){return(ht=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var pt=function(e){function Condition(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Condition),dt(this,Condition,[e])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ht(e,t)}(Condition,e),t=Condition,(n=[{key:"height",value:function(e,t){return this.predicate("h",e,t)}},{key:"width",value:function(e,t){return this.predicate("w",e,t)}},{key:"aspectRatio",value:function(e,t){return this.predicate("ar",e,t)}},{key:"pageCount",value:function(e,t){return this.predicate("pc",e,t)}},{key:"faceCount",value:function(e,t){return this.predicate("fc",e,t)}},{key:"duration",value:function(e,t){return this.predicate("du",e,t)}},{key:"initialDuration",value:function(e,t){return this.predicate("idu",e,t)}}])&&lt(t.prototype,n),o&&lt(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(ut);function yt(e){return(yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,s,u=[],a=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(o=i.call(n)).done)&&(u.push(o.value),u.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{if(!a&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(l)throw r}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return _t(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _t(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function vt(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,bt(o.key),o)}}function bt(e){var t=function(e,t){if("object"!=yt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=yt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yt(t)?t:t+""}var gt=function(){return e=function Configuration(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Configuration),this.configuration=null==e?{}:g()(e),ve(this.configuration,jt)},(t=[{key:"init",value:function(){return this.fromEnvironment(),this.fromDocument(),this}},{key:"set",value:function(e,t){return this.configuration[e]=t,this}},{key:"get",value:function(e){return this.configuration[e]}},{key:"merge",value:function(e){return v()(this.configuration,g()(e)),this}},{key:"fromDocument",value:function(){var e,t,n,o;if(o="undefined"!=typeof document&&null!==document?document.querySelectorAll('meta[name^="cloudinary_"]'):void 0)for(t=0,n=o.length;t<n;t++)e=o[t],this.configuration[e.getAttribute("name").replace("cloudinary_","")]=e.getAttribute("content");return this}},{key:"fromEnvironment",value:function(){var e,t,n,o=this;return"undefined"!=typeof process&&null!==process&&process.env&&process.env.CLOUDINARY_URL&&(e=process.env.CLOUDINARY_URL,(n=/cloudinary:\/\/(?:(\w+)(?:\:([\w-]+))?@)?([\w\.-]+)(?:\/([^?]*))?(?:\?(.+))?/.exec(e))&&(null!=n[3]&&(this.configuration.cloud_name=n[3]),null!=n[1]&&(this.configuration.api_key=n[1]),null!=n[2]&&(this.configuration.api_secret=n[2]),null!=n[4]&&(this.configuration.private_cdn=null!=n[4]),null!=n[4]&&(this.configuration.secure_distribution=n[4]),null!=(t=n[5])&&t.split("&").forEach((function(e){var t=mt(e.split("="),2),n=t[0],r=t[1];null==r&&(r=!0),o.configuration[n]=r})))),this}},{key:"config",value:function(e,t){switch(!1){case void 0===t:return this.set(e,t),this.configuration;case!R()(e):return this.get(e);case!T()(e):return this.merge(e),this.configuration;default:return this.configuration}}},{key:"toOptions",value:function(){return g()(this.configuration)}}])&&vt(e.prototype,t),n&&vt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}(),jt={responsive_class:"cld-responsive",responsive_use_breakpoints:!0,round_dpr:!0,secure:"https:"===("undefined"!=typeof window&&null!==window&&window.location?window.location.protocol:void 0)};gt.CONFIG_PARAMS=["api_key","api_secret","callback","cdn_subdomain","cloud_name","cname","private_cdn","protocol","resource_type","responsive","responsive_class","responsive_use_breakpoints","responsive_width","round_dpr","secure","secure_cdn_subdomain","secure_distribution","shorten","type","upload_preset","url_suffix","use_root_path","version","externalLibraries","max_timeout_ms"];var wt=gt;function At(e){return(At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ot(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Dt(o.key),o)}}function Dt(e){var t=function(e,t){if("object"!=At(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=At(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==At(t)?t:t+""}var Et=function(){return e=function Layer(e){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Layer),this.options={},null!=e&&["resourceType","type","publicId","format"].forEach((function(n){var o;return t.options[n]=null!=(o=e[n])?o:e[Ee(n)]}))},(t=[{key:"resourceType",value:function(e){return this.options.resourceType=e,this}},{key:"type",value:function(e){return this.options.type=e,this}},{key:"publicId",value:function(e){return this.options.publicId=e,this}},{key:"getPublicId",value:function(){var e;return null!=(e=this.options.publicId)?e.replace(/\//g,":"):void 0}},{key:"getFullPublicId",value:function(){return null!=this.options.format?this.getPublicId()+"."+this.options.format:this.getPublicId()}},{key:"format",value:function(e){return this.options.format=e,this}},{key:"toString",value:function(){var e;if(e=[],null==this.options.publicId)throw"Must supply publicId";return"image"!==this.options.resourceType&&e.push(this.options.resourceType),"upload"!==this.options.type&&e.push(this.options.type),e.push(this.getFullPublicId()),w()(e).join(":")}},{key:"clone",value:function(){return new this.constructor(this.options)}}])&&Ot(e.prototype,t),n&&Ot(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();function Bt(e){return(Bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ct(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,St(o.key),o)}}function St(e){var t=function(e,t){if("object"!=Bt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Bt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Bt(t)?t:t+""}function kt(e,t,n){return t=xt(t),function(e,t){if(t&&("object"===Bt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],xt(e).constructor):t.apply(e,n))}function xt(e){return(xt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Ft(e,t){return(Ft=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Pt=function(e){function TextLayer(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,TextLayer),t=kt(this,TextLayer,[e]),null!=e&&["resourceType","resourceType","fontFamily","fontSize","fontWeight","fontStyle","textDecoration","textAlign","stroke","letterSpacing","lineSpacing","fontHinting","fontAntialiasing","text","textStyle"].forEach((function(n){var o;return t.options[n]=null!=(o=e[n])?o:e[Ee(n)]})),t.options.resourceType="text",t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ft(e,t)}(TextLayer,e),t=TextLayer,(n=[{key:"resourceType",value:function(e){throw"Cannot modify resourceType for text layers"}},{key:"type",value:function(e){throw"Cannot modify type for text layers"}},{key:"format",value:function(e){throw"Cannot modify format for text layers"}},{key:"fontFamily",value:function(e){return this.options.fontFamily=e,this}},{key:"fontSize",value:function(e){return this.options.fontSize=e,this}},{key:"fontWeight",value:function(e){return this.options.fontWeight=e,this}},{key:"fontStyle",value:function(e){return this.options.fontStyle=e,this}},{key:"textDecoration",value:function(e){return this.options.textDecoration=e,this}},{key:"textAlign",value:function(e){return this.options.textAlign=e,this}},{key:"stroke",value:function(e){return this.options.stroke=e,this}},{key:"letterSpacing",value:function(e){return this.options.letterSpacing=e,this}},{key:"lineSpacing",value:function(e){return this.options.lineSpacing=e,this}},{key:"fontHinting",value:function(e){return this.options.fontHinting=e,this}},{key:"fontAntialiasing",value:function(e){return this.options.fontAntialiasing=e,this}},{key:"text",value:function(e){return this.options.text=e,this}},{key:"textStyle",value:function(e){return this.options.textStyle=e,this}},{key:"toString",value:function(){var e,t,n,o,r,i,s,u,a,l;if(u=this.textStyleIdentifier(),null!=this.options.publicId&&(o=this.getFullPublicId()),null!=this.options.text){if(t=!Ie(o),n=!Ie(u),t&&n||!t&&!n)throw"Must supply either style parameters or a public_id when providing text parameter in a text overlay/underlay, but not both!";for(r=/\$\([a-zA-Z]\w*\)/g,s=0,l=_e(this.options.text,/[,\/]/g),a="";i=r.exec(l);)a+=_e(l.slice(s,i.index)),a+=i[0],s=i.index+i[0].length;a+=_e(l.slice(s))}return e=[this.options.resourceType,u,o,a],w()(e).join(":")}},{key:"textStyleIdentifier",value:function(){if(!Ie(this.options.textStyle))return this.options.textStyle;var e;if(e=[],"normal"!==this.options.fontWeight&&e.push(this.options.fontWeight),"normal"!==this.options.fontStyle&&e.push(this.options.fontStyle),"none"!==this.options.textDecoration&&e.push(this.options.textDecoration),e.push(this.options.textAlign),"none"!==this.options.stroke&&e.push(this.options.stroke),Ie(this.options.letterSpacing)&&!me(this.options.letterSpacing)||e.push("letter_spacing_"+this.options.letterSpacing),Ie(this.options.lineSpacing)&&!me(this.options.lineSpacing)||e.push("line_spacing_"+this.options.lineSpacing),Ie(this.options.fontAntialiasing)||e.push("antialias_"+this.options.fontAntialiasing),Ie(this.options.fontHinting)||e.push("hinting_"+this.options.fontHinting),!Ie(w()(e))){if(Ie(this.options.fontFamily))throw"Must supply fontFamily. ".concat(e);if(Ie(this.options.fontSize)&&!me(this.options.fontSize))throw"Must supply fontSize."}return e.unshift(this.options.fontFamily,this.options.fontSize),e=w()(e).join("_")}}])&&Ct(t.prototype,n),o&&Ct(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(Et);function Tt(e){return(Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function It(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Rt(o.key),o)}}function Rt(e){var t=function(e,t){if("object"!=Tt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Tt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Tt(t)?t:t+""}function Lt(e,t,n){return t=zt(t),function(e,t){if(t&&("object"===Tt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],zt(e).constructor):t.apply(e,n))}function zt(e){return(zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Mt(e,t){return(Mt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Nt=function(e){function SubtitlesLayer(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,SubtitlesLayer),(t=Lt(this,SubtitlesLayer,[e])).options.resourceType="subtitles",t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mt(e,t)}(SubtitlesLayer,e),t=SubtitlesLayer,n&&It(t.prototype,n),o&&It(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(Pt);function Vt(e){return(Vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ut(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Ht(o.key),o)}}function Ht(e){var t=function(e,t){if("object"!=Vt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Vt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Vt(t)?t:t+""}function Wt(e,t,n){return t=$t(t),function(e,t){if(t&&("object"===Vt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],$t(e).constructor):t.apply(e,n))}function $t(e){return($t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Gt(e,t){return(Gt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Kt=function(e){function FetchLayer(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,FetchLayer),t=Wt(this,FetchLayer,[e]),R()(e)?t.options.url=e:(null!=e?e.url:void 0)&&(t.options.url=e.url),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gt(e,t)}(FetchLayer,e),t=FetchLayer,(n=[{key:"url",value:function(e){return this.options.url=e,this}},{key:"toString",value:function(){return"fetch:".concat(xe(this.options.url))}}])&&Ut(t.prototype,n),o&&Ut(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(Et);function qt(e,t,n){return t=Zt(t),function(e,t){if(t&&("object"===en(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],Zt(e).constructor):t.apply(e,n))}function Yt(){return(Yt="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=Qt(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(this,arguments)}function Qt(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Zt(e)););return e}function Zt(e){return(Zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Xt(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jt(e,t)}function Jt(e,t){return(Jt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function en(e){return(en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,rn(o.key),o)}}function on(e,t,n){return t&&nn(e.prototype,t),n&&nn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function rn(e){var t=function(e,t){if("object"!=en(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=en(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==en(t)?t:t+""}var sn=function(){return on((function e(t,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:C.a;tn(this,e),this.name=t,this.shortName=n,this.process=o}),[{key:"set",value:function(e){return this.origValue=e,this}},{key:"serialize",value:function(){var e,t;return e=this.value(),t=F()(e)||T()(e)||R()(e)?!Ie(e):null!=e,null!=this.shortName&&t?"".concat(this.shortName,"_").concat(e):""}},{key:"value",value:function(){return this.process(this.origValue)}}],[{key:"norm_color",value:function(e){return null!=e?e.replace(/^#/,"rgb:"):void 0}},{key:"build_array",value:function(e){return null==e?[]:F()(e)?e:[e]}},{key:"process_video_params",value:function(e){var t;switch(e.constructor){case Object:return t="","codec"in e&&(t=e.codec,"profile"in e&&(t+=":"+e.profile,"level"in e&&(t+=":"+e.level,"b_frames"in e&&!1===e.b_frames&&(t+=":bframes_no")))),t;case String:return e;default:return null}}}])}(),un=function(e){function t(e,n){var o,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return tn(this,t),(o=qt(this,t,[e,n,i])).sep=r,o}return Xt(t,e),on(t,[{key:"serialize",value:function(){if(null!=this.shortName){var e=this.value();if(Ie(e))return"";if(R()(e))return"".concat(this.shortName,"_").concat(e);var t=e.map((function(e){return U()(e.serialize)?e.serialize():e})).join(this.sep);return"".concat(this.shortName,"_").concat(t)}return""}},{key:"value",value:function(){var e=this;return F()(this.origValue)?this.origValue.map((function(t){return e.process(t)})):this.process(this.origValue)}},{key:"set",value:function(e){return null==e||F()(e)?Yt(Zt(t.prototype),"set",this).call(this,e):Yt(Zt(t.prototype),"set",this).call(this,[e])}}])}(sn),an=function(e){function t(e){var n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"t",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return tn(this,t),(n=qt(this,t,[e,o,i])).sep=r,n}return Xt(t,e),on(t,[{key:"serialize",value:function(){var e=this,t="",n=this.value();if(Ie(n))return t;if(pe(n)){var o=n.join(this.sep);Ie(o)||(t="".concat(this.shortName,"_").concat(o))}else t=n.map((function(t){return R()(t)&&!Ie(t)?"".concat(e.shortName,"_").concat(t):U()(t.serialize)?t.serialize():T()(t)&&!Ie(t)?new Cn(t).serialize():void 0})).filter((function(e){return e}));return t}},{key:"set",value:function(e){return this.origValue=e,F()(this.origValue)?Yt(Zt(t.prototype),"set",this).call(this,this.origValue):Yt(Zt(t.prototype),"set",this).call(this,[this.origValue])}}])}(sn),ln=function(e){function t(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.norm_range_value;return tn(this,t),qt(this,t,[e,n,o])}return Xt(t,e),on(t,null,[{key:"norm_range_value",value:function(e){var t=String(e).match(new RegExp("^(([0-9]*)\\.([0-9]+)|([0-9]+))([%pP])?$"));if(t){var n=null!=t[5]?"p":"";e=(t[1]||t[4])+n}return ut.normalize(e)}}])}(sn),cn=function(e){function t(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:C.a;return tn(this,t),qt(this,t,[e,n,o])}return Xt(t,e),on(t,[{key:"serialize",value:function(){return this.value()}}])}(sn),dn=function(e){function t(){return tn(this,t),qt(this,t,arguments)}return Xt(t,e),on(t,[{key:"value",value:function(){if(null==this.origValue)return"";var e;if(this.origValue instanceof Et)e=this.origValue;else if(T()(this.origValue)){var t=Ce(this.origValue);e="text"===t.resourceType||null!=t.text?new Pt(t):"subtitles"===t.resourceType?new Nt(t):"fetch"===t.resourceType||null!=t.url?new Kt(t):new Et(t)}else e=R()(this.origValue)?/^fetch:.+/.test(this.origValue)?new Kt(this.origValue.substr(6)):this.origValue:"";return e.toString()}}],[{key:"textStyle",value:function(e){return new Pt(e).textStyleIdentifier()}}])}(sn);function fn(e,t,n){return t=hn(t),function(e,t){if(t&&("object"===_n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],hn(e).constructor):t.apply(e,n))}function hn(e){return(hn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pn(e,t){return(pn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,s,u=[],a=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(o=i.call(n)).done)&&(u.push(o.value),u.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{if(!a&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(l)throw r}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return mn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return mn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function _n(e){return(_n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function vn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,jn(o.key),o)}}function gn(e,t,n){return t&&bn(e.prototype,t),n&&bn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function jn(e){var t=function(e,t){if("object"!=_n(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==_n(t)?t:t+""}function wn(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return n.forEach((function(t){Object.keys(t).forEach((function(n){null!=t[n]&&(e[n]=t[n])}))})),e}var An=function(){function e(t){var n,o;vn(this,e),n=void 0,o={},this.toOptions=function(e){var t={};if(null==e&&(e=!0),Object.keys(o).forEach((function(e){return t[e]=o[e].origValue})),wn(t,this.otherOptions),e&&!Ie(this.chained)){var n=this.chained.map((function(e){return e.toOptions()}));n.push(t),wn(t={},this.otherOptions),t.transformation=n}return t},this.setParent=function(e){return n=e,null!=e&&this.fromOptions("function"==typeof e.toOptions?e.toOptions():void 0),this},this.getParent=function(){return n},this.param=function(e,t,n,r,i){return null==i&&(i=U()(r)?r:C.a),o[t]=new sn(t,n,i).set(e),this},this.rawParam=function(e,t,n,r,i){return i=Dn(arguments),o[t]=new cn(t,n,i).set(e),this},this.rangeParam=function(e,t,n,r,i){return i=Dn(arguments),o[t]=new ln(t,n,i).set(e),this},this.arrayParam=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:":",i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;return i=Dn(arguments),o[t]=new un(t,n,r,i).set(e),this},this.transformationParam=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;return i=Dn(arguments),o[t]=new an(t,n,r,i).set(e),this},this.layerParam=function(e,t,n){return o[t]=new dn(t,n).set(e),this},this.getValue=function(e){var t=o[e]&&o[e].value();return null!=t?t:this.otherOptions[e]},this.get=function(e){return o[e]},this.remove=function(e){var t;switch(!1){case null==o[e]:return t=o[e],delete o[e],t.origValue;case null==this.otherOptions[e]:return t=this.otherOptions[e],delete this.otherOptions[e],t;default:return null}},this.keys=function(){var e;return function(){var t;for(e in t=[],o)null!=e&&t.push(e.match(On)?e:Ee(e));return t}().sort()},this.toPlainObject=function(){var e,t,n;for(t in e={},o)e[t]=o[t].value(),T()(e[t])&&(e[t]=g()(e[t]));return Ie(this.chained)||((n=this.chained.map((function(e){return e.toPlainObject()}))).push(e),e={transformation:n}),e},this.chain=function(){var e;return 0!==Object.getOwnPropertyNames(o).length&&(e=new this.constructor(this.toOptions(!1)),this.resetTransformations(),this.chained.push(e)),this},this.resetTransformations=function(){return o={},this},this.otherOptions={},this.chained=[],this.fromOptions(t)}return gn(e,[{key:"fromOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(t instanceof e)this.fromTransformation(t);else for(var n in(R()(t)||F()(t))&&(t={transformation:t}),(t=g()(t,(function(t){if(t instanceof e||t instanceof Layer)return new t.clone}))).if&&(this.set("if",t.if),delete t.if),t){var o=t[n];null!=o&&(n.match(On)?"$attr"!==n&&this.set("variable",n,o):this.set(n,o))}return this}},{key:"fromTransformation",value:function(t){var n=this;return t instanceof e&&t.keys().forEach((function(e){return n.set(e,t.get(e).origValue)})),this}},{key:"set",value:function(e){var t;t=De(e);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return k()(Bn.methods,t)?this[t].apply(this,o):this.otherOptions[e]=o[0],this}},{key:"hasLayer",value:function(){return this.getValue("overlay")||this.getValue("underlay")}},{key:"serialize",value:function(){var e,t,n,o,r,i,s,u,a,l,c,d,f,h,p,y,m;for(l=this.chained.map((function(e){return e.serialize()})),o=this.keys(),h=null!=(r=this.get("transformation"))?r.serialize():void 0,e=null!=(i=this.get("if"))?i.serialize():void 0,y=function(e){var t,n,o,r,i;if(F()(e)){for(r=[],t=0,n=e.length;t<n;t++){var s=yn(e[t],2);o=s[0],i=s[1],r.push("".concat(o,"_").concat(ut.normalize(i)))}return r}return e}(null!=(s=this.get("variables"))?s.value():void 0),o=O()(o,["transformation","if","variables"]),m=[],d=[],t=0,n=o.length;t<n;t++)(c=o[t]).match(On)?m.push(c+"_"+ut.normalize(null!=(u=this.get(c))?u.value():void 0)):d.push(null!=(a=this.get(c))?a.serialize():void 0);switch(!1){case!R()(h):d.push(h);break;case!F()(h):l=l.concat(h)}return d=function(){var e,t,n;for(n=[],e=0,t=d.length;e<t;e++)p=d[e],(F()(p)&&!Ie(p)||!F()(p)&&p)&&n.push(p);return n}(),d=m.sort().concat(y).concat(d.sort()),"if_end"===e?d.push(e):Ie(e)||d.unshift(e),Ie(f=w()(d).join(this.param_separator))||l.push(f),w()(l).join(this.trans_separator)}},{key:"toHtmlAttributes",value:function(){var e,t,n,o,r,i,s,u,a=this;return n={},Object.keys(this.otherOptions).forEach((function(t){i=a.otherOptions[t],u=Ee(t),k()(Bn.PARAM_NAMES,u)||k()(ce,u)||(e=/^html_/.test(t)?t.slice(5):t,n[e]=i)})),this.keys().forEach((function(e){/^html_/.test(e)&&(n[De(e.slice(5))]=a.getValue(e))})),this.hasLayer()||this.getValue("angle")||k()(["fit","limit","lfill"],this.getValue("crop"))||(s=null!=(o=this.get("width"))?o.origValue:void 0,t=null!=(r=this.get("height"))?r.origValue:void 0,parseFloat(s)>=1&&null==n.width&&(n.width=s),parseFloat(t)>=1&&null==n.height&&(n.height=t)),n}},{key:"toHtml",value:function(){var e;return null!=(e=this.getParent())&&"function"==typeof e.toHtml?e.toHtml():void 0}},{key:"toString",value:function(){return this.serialize()}},{key:"clone",value:function(){return new this.constructor(this.toOptions(!0))}}],[{key:"listNames",value:function(){return Bn.methods}},{key:"isValidParamName",value:function(e){return Bn.methods.indexOf(De(e))>=0}}])}(),On=/^\$[a-zA-Z0-9]+$/;function Dn(e){var t;return t=null!=e?e[e.length-1]:void 0,U()(t)?t:void 0}function En(e){var t=e.function_type,n=e.source;return"remote"===t?[t,btoa(n)].join(":"):"wasm"===t?[t,n].join(":"):void 0}An.prototype.trans_separator="/",An.prototype.param_separator=",";var Bn=function(e){function Transformation(e){return vn(this,Transformation),fn(this,Transformation,[e])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pn(e,t)}(Transformation,e),gn(Transformation,[{key:"angle",value:function(e){return this.arrayParam(e,"angle","a",".",ut.normalize)}},{key:"audioCodec",value:function(e){return this.param(e,"audio_codec","ac")}},{key:"audioFrequency",value:function(e){return this.param(e,"audio_frequency","af")}},{key:"aspectRatio",value:function(e){return this.param(e,"aspect_ratio","ar",ut.normalize)}},{key:"background",value:function(e){return this.param(e,"background","b",sn.norm_color)}},{key:"bitRate",value:function(e){return this.param(e,"bit_rate","br")}},{key:"border",value:function(e){return this.param(e,"border","bo",(function(e){return T()(e)?(e=v()({},{color:"black",width:2},e),"".concat(e.width,"px_solid_").concat(sn.norm_color(e.color))):e}))}},{key:"color",value:function(e){return this.param(e,"color","co",sn.norm_color)}},{key:"colorSpace",value:function(e){return this.param(e,"color_space","cs")}},{key:"crop",value:function(e){return this.param(e,"crop","c")}},{key:"customFunction",value:function(e){return this.param(e,"custom_function","fn",(function(){return En(e)}))}},{key:"customPreFunction",value:function(e){if(!this.get("custom_function"))return this.rawParam(e,"custom_function","",(function(){return(e=En(e))?"fn_pre:".concat(e):e}))}},{key:"defaultImage",value:function(e){return this.param(e,"default_image","d")}},{key:"delay",value:function(e){return this.param(e,"delay","dl")}},{key:"density",value:function(e){return this.param(e,"density","dn")}},{key:"duration",value:function(e){return this.rangeParam(e,"duration","du")}},{key:"dpr",value:function(e){return this.param(e,"dpr","dpr",(function(e){return(null!=(e=e.toString())?e.match(/^\d+$/):void 0)?e+".0":ut.normalize(e)}))}},{key:"effect",value:function(e){return this.arrayParam(e,"effect","e",":",ut.normalize)}},{key:"else",value:function(){return this.if("else")}},{key:"endIf",value:function(){return this.if("end")}},{key:"endOffset",value:function(e){return this.rangeParam(e,"end_offset","eo")}},{key:"fallbackContent",value:function(e){return this.param(e,"fallback_content")}},{key:"fetchFormat",value:function(e){return this.param(e,"fetch_format","f")}},{key:"format",value:function(e){return this.param(e,"format")}},{key:"flags",value:function(e){return this.arrayParam(e,"flags","fl",".")}},{key:"gravity",value:function(e){return this.param(e,"gravity","g")}},{key:"fps",value:function(e){return this.param(e,"fps","fps",(function(e){return R()(e)?e:F()(e)?e.join("-"):e}))}},{key:"height",value:function(e){var t=this;return this.param(e,"height","h",(function(){return t.getValue("crop")||t.getValue("overlay")||t.getValue("underlay")?ut.normalize(e):null}))}},{key:"htmlHeight",value:function(e){return this.param(e,"html_height")}},{key:"htmlWidth",value:function(e){return this.param(e,"html_width")}},{key:"if",value:function(){var e,t,n,o,r,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";switch(i){case"else":return this.chain(),this.param(i,"if","if");case"end":for(this.chain(),e=n=this.chained.length-1;n>=0&&"end"!==(t=this.chained[e].getValue("if"))&&(null==t||(o=Transformation.new().if(t),this.chained[e].remove("if"),r=this.chained[e],this.chained[e]=Transformation.new().transformation([o,r]),"else"===t));e=n+=-1);return this.param(i,"if","if");case"":return pt.new().setParent(this);default:return this.param(i,"if","if",(function(e){return pt.new(e).toString()}))}}},{key:"keyframeInterval",value:function(e){return this.param(e,"keyframe_interval","ki")}},{key:"ocr",value:function(e){return this.param(e,"ocr","ocr")}},{key:"offset",value:function(e){var t,n,o=yn(U()(null!=e?e.split:void 0)?e.split(".."):F()(e)?e:[null,null],2);if(n=o[0],t=o[1],null!=n&&this.startOffset(n),null!=t)return this.endOffset(t)}},{key:"opacity",value:function(e){return this.param(e,"opacity","o",ut.normalize)}},{key:"overlay",value:function(e){return this.layerParam(e,"overlay","l")}},{key:"page",value:function(e){return this.param(e,"page","pg")}},{key:"poster",value:function(e){return this.param(e,"poster")}},{key:"prefix",value:function(e){return this.param(e,"prefix","p")}},{key:"quality",value:function(e){return this.param(e,"quality","q",ut.normalize)}},{key:"radius",value:function(e){return this.arrayParam(e,"radius","r",":",ut.normalize)}},{key:"rawTransformation",value:function(e){return this.rawParam(e,"raw_transformation")}},{key:"size",value:function(e){var t,n;if(U()(null!=e?e.split:void 0)){var o=yn(e.split("x"),2);return n=o[0],t=o[1],this.width(n),this.height(t)}}},{key:"sourceTypes",value:function(e){return this.param(e,"source_types")}},{key:"sourceTransformation",value:function(e){return this.param(e,"source_transformation")}},{key:"startOffset",value:function(e){return this.rangeParam(e,"start_offset","so")}},{key:"streamingProfile",value:function(e){return this.param(e,"streaming_profile","sp")}},{key:"transformation",value:function(e){return this.transformationParam(e,"transformation","t")}},{key:"underlay",value:function(e){return this.layerParam(e,"underlay","u")}},{key:"variable",value:function(e,t){return this.param(t,e,e)}},{key:"variables",value:function(e){return this.arrayParam(e,"variables")}},{key:"videoCodec",value:function(e){return this.param(e,"video_codec","vc",sn.process_video_params)}},{key:"videoSampling",value:function(e){return this.param(e,"video_sampling","vs")}},{key:"width",value:function(e){var t=this;return this.param(e,"width","w",(function(){return t.getValue("crop")||t.getValue("overlay")||t.getValue("underlay")?ut.normalize(e):null}))}},{key:"x",value:function(e){return this.param(e,"x","x",ut.normalize)}},{key:"y",value:function(e){return this.param(e,"y","y",ut.normalize)}},{key:"zoom",value:function(e){return this.param(e,"zoom","z",ut.normalize)}}],[{key:"new",value:function(e){return new Transformation(e)}}])}(An);Bn.methods=["angle","audioCodec","audioFrequency","aspectRatio","background","bitRate","border","color","colorSpace","crop","customFunction","customPreFunction","defaultImage","delay","density","duration","dpr","effect","else","endIf","endOffset","fallbackContent","fetchFormat","format","flags","gravity","fps","height","htmlHeight","htmlWidth","if","keyframeInterval","ocr","offset","opacity","overlay","page","poster","prefix","quality","radius","rawTransformation","size","sourceTypes","sourceTransformation","startOffset","streamingProfile","transformation","underlay","variable","variables","videoCodec","videoSampling","width","x","y","zoom"],Bn.PARAM_NAMES=Bn.methods.map(Ee).concat(wt.CONFIG_PARAMS);var Cn=Bn;function Sn(e){return(Sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function kn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,xn(o.key),o)}}function xn(e){var t=function(e,t){if("object"!=Sn(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Sn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Sn(t)?t:t+""}function Fn(e,t){return t?!0===t?e:"".concat(e,'="').concat(t,'"'):void 0}function Pn(e){return R()(e)?e.replace('"',"&#34;").replace("'","&#39;"):e}var Tn=function(){return e=function HtmlTag(e,t,n){var o;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,HtmlTag),this.name=e,this.publicId=t,null==n&&(T()(t)?(n=t,this.publicId=void 0):n={}),(o=new Cn(n)).setParent(this),this.transformation=function(){return o}},n=[{key:"new",value:function(e,t,n){return new this(e,t,n)}},{key:"isResponsive",value:function(e,t){var n;return n=Ve(e,"src-cache")||Ve(e,"src"),Ke(e,t)&&/\bw_auto\b/.exec(n)}}],(t=[{key:"htmlAttrs",value:function(e){var t,n;return function(){var o;for(t in o=[],e)(n=Pn(e[t]))&&o.push(Fn(t,n));return o}().sort().join(" ")}},{key:"getOptions",value:function(){return this.transformation().toOptions()}},{key:"getOption",value:function(e){return this.transformation().getValue(e)}},{key:"attributes",value:function(){var e=this.transformation().toHtmlAttributes();return Object.keys(e).forEach((function(t){T()(e[t])&&delete e[t]})),e.attributes&&(z()(e,e.attributes),delete e.attributes),e}},{key:"setAttr",value:function(e,t){return this.transformation().set("html_".concat(e),t),this}},{key:"getAttr",value:function(e){return this.attributes()["html_".concat(e)]||this.attributes()[e]}},{key:"removeAttr",value:function(e){var t;return null!=(t=this.transformation().remove("html_".concat(e)))?t:this.transformation().remove(e)}},{key:"content",value:function(){return""}},{key:"openTag",value:function(){var e="<"+this.name,t=this.htmlAttrs(this.attributes());return t&&t.length>0&&(e+=" "+t),e+">"}},{key:"closeTag",value:function(){return"</".concat(this.name,">")}},{key:"toHtml",value:function(){return this.openTag()+this.content()+this.closeTag()}},{key:"toDOM",value:function(){var e,t,n,o;if(!U()("undefined"!=typeof document&&null!==document?document.createElement:void 0))throw"Can't create DOM if document is not present!";for(t in e=document.createElement(this.name),n=this.attributes())o=n[t],e.setAttribute(t,o);return e}}])&&kn(e.prototype,t),n&&kn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}(),In=["placeholder","accessibility"];function Rn(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ln(e){return!!e&&!!e.match(/^https?:\//)}function zn(e,t){if(t.cloud_name&&"/"===t.cloud_name[0])return"/res"+t.cloud_name;var n="http://",o="",r="res",i=".cloudinary.com",u="/"+t.cloud_name;return t.protocol&&(n=t.protocol+"//"),t.private_cdn&&(o=t.cloud_name+"-",u=""),t.cdn_subdomain&&(r="res-"+function(e){return s(e)%5+1}(e)),t.secure?(n="https://",!1===t.secure_cdn_subdomain&&(r="res"),null!=t.secure_distribution&&t.secure_distribution!==Z&&t.secure_distribution!==J&&(o="",r="",i=t.secure_distribution)):t.cname&&(n="http://",o="",r=t.cdn_subdomain?"a"+(s(e)%5+1)+".":"",i=t.cname),[n,o,r,i,u].join("")}function Mn(e){return encodeURIComponent(e).replace(/%3A/g,":").replace(/%2F/g,"/")}function Nn(e){var t=e.cloud_name,n=e.url_suffix;return t?n&&n.match(/[\.\/]/)?"url_suffix should not include . or /":void 0:"Unknown cloud_name"}function Vn(e,t){var n,o,r=t.type;return Ln(e)||"fetch"!==r?e:(n=e,o=document.location.protocol+"//"+document.location.host,"?"===n[0]?o+=document.location.pathname:"/"!==n[0]&&(o+=document.location.pathname.replace(/\/[^\/]*$/,"/")),o+n)}function Un(e,t){if(Ln(e)&&("upload"===t.type||"asset"===t.type))return e;var n=function(e,t){var n=t.force_version||void 0===t.force_version,o=e.indexOf("/")<0||e.match(/^v[0-9]+/)||Ln(e)||t.version;return n&&!o&&(t.version=1),t.version?"v".concat(t.version):""}(e,t),o=function(e){var t=e||{},n=t.placeholder,o=t.accessibility,r=Rn(t,In),i=new Cn(r);return o&&le[o]&&i.chain().effect(le[o]),n&&("predominant-color"===n&&i.getValue("width")&&i.getValue("height")&&(n+="-pixel"),(ae[n]||ae.blur).forEach((function(e){return i.chain().transformation(e)}))),i.serialize()}(t),r=zn(e,t),i=function(e){var t=e.signature,n=!t||0===t.indexOf("s--")&&"--"===t.substr(-2);return delete e.signature,n?t:"s--".concat(t,"--")}(t),s=function(e){var t,n=e.resource_type,o=void 0===n?"image":n,r=e.type,i=void 0===r?"upload":r,s=e.url_suffix,u=e.use_root_path,a=e.shorten,l=o;if(T()(l)&&(l=(t=l).resource_type,i=t.type,a=t.shorten),null==i&&(i="upload"),null!=s&&(l=oe["".concat(l,"/").concat(i)],i=null,null==l))throw new Error("URL Suffix only supported for ".concat(Object.keys(oe).join(", ")));if(u){if(("image"!==l||"upload"!==i)&&"images"!==l)throw new Error("Root path only supported for image/upload");l=null,i=null}return a&&"image"===l&&"upload"===i&&(l="iu",i=null),[l,i].join("/")}(t);return e=function(e,t){if(Ln(e))e=Mn(e);else{try{e=decodeURIComponent(e)}catch(n){}e=Mn(e),t.url_suffix&&(e=e+"/"+t.url_suffix),t.format&&(t.trust_public_id||(e=e.replace(/\.(jpg|png|gif|webp)$/,"")),e=e+"."+t.format)}return e}(e,t),w()([r,s,i,o,n,e]).join("/").replace(/([^:])\/+/g,"$1/").replace(" ","%20")}function Hn(e,t){return e instanceof Cn&&(e=e.toOptions()),"fetch"===(e=ve({},e,t,re)).type&&(e.fetch_format=e.fetch_format||e.format),e}function Wn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!e)return e;e=Vn(e,t=Hn(t,n));var o=Nn(t);if(o)throw o;var r=Un(e,t);if(t.urlAnalytics){var i=m(t),s=p(i),u="?";r.indexOf("?")>=0&&(u="&"),r="".concat(r).concat(u,"_a=").concat(s)}if(t.auth_token){var a=r.indexOf("?")>=0?"&":"?";r="".concat(r).concat(a,"__cld_token__=").concat(t.auth_token)}return r}function $n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,s,u=[],a=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(o=i.call(n)).done)&&(u.push(o.value),u.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{if(!a&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(l)throw r}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Gn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Gn(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function Kn(e){var t=e.breakpoints||[];if(t.length)return t;var n=$n([e.min_width,e.max_width,e.max_images].map(Number),3),o=n[0],r=n[1],i=n[2];if([o,r,i].some(isNaN))throw"Either (min_width, max_width, max_images) or breakpoints must be provided to the image srcset attribute";if(o>r)throw"min_width must be less than max_width";if(i<=0)throw"max_images must be a positive integer";1===i&&(o=r);for(var s=Math.ceil((r-o)/Math.max(i-1,1)),u=o;u<r;u+=s)t.push(u);return t.push(r),t}var qn=Ie;function Yn(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=Fe(o);return n=n||o,r.raw_transformation=new Cn([z.a({},n),{crop:"scale",width:t}]).toString(),Wn(e,r)}function Qn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Kn(t)}function Zn(e,t,n,o){return Pe(o=g.a(o)),t.map((function(t){return"".concat(Yn(e,t,n,o)," ").concat(t,"w")})).join(", ")}function Xn(e){return null==e?"":e.map((function(e){return"(max-width: ".concat(e,"px) ").concat(e,"px")})).join(", ")}function Jn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r={};if(qn(n))return r;var i=!t.sizes&&!0===n.sizes,s=!t.srcset;if(s||i){var u=Qn(e,n,o);if(s){var a=n.transformation,l=Zn(e,u,a,o);qn(l)||(r.srcset=l)}if(i){var c=Xn(u);qn(c)||(r.sizes=c)}}return r}function eo(e){return(eo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function to(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function no(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,oo(o.key),o)}}function oo(e){var t=function(e,t){if("object"!=eo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=eo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eo(t)?t:t+""}function ro(e,t,n){return t=uo(t),function(e,t){if(t&&("object"===eo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],uo(e).constructor):t.apply(e,n))}function io(){return(io="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=so(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(this,arguments)}function so(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=uo(e)););return e}function uo(e){return(uo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ao(e,t){return(ao=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var lo=function(e){function ImageTag(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return to(this,ImageTag),ro(this,ImageTag,["img",e,t])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ao(e,t)}(ImageTag,e),t=ImageTag,(n=[{key:"closeTag",value:function(){return""}},{key:"attributes",value:function(){var e,t,n;e=io(uo(ImageTag.prototype),"attributes",this).call(this)||{},t=this.getOptions();var o=this.getOption("attributes")||{},r=this.getOption("srcset")||o.srcset,i={};return R()(r)?i.srcset=r:i=Jn(this.publicId,o,r,t),Ie(i)||(delete e.width,delete e.height),z()(e,i),null==e[n=t.responsive&&!t.client_hints?"data-src":"src"]&&(e[n]=Wn(this.publicId,this.getOptions())),e}}])&&no(t.prototype,n),o&&no(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(Tn);function co(e){return(co="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fo(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ho(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,po(o.key),o)}}function po(e){var t=function(e,t){if("object"!=co(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=co(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==co(t)?t:t+""}function yo(e,t,n){return t=vo(t),function(e,t){if(t&&("object"===co(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],vo(e).constructor):t.apply(e,n))}function mo(){return(mo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=_o(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(this,arguments)}function _o(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=vo(e)););return e}function vo(e){return(vo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function bo(e,t){return(bo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var go=function(e){function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return fo(this,t),yo(this,t,["source",e,n])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&bo(e,t)}(t,e),n=t,(o=[{key:"closeTag",value:function(){return""}},{key:"attributes",value:function(){var e=this.getOption("srcset"),n=mo(vo(t.prototype),"attributes",this).call(this)||{},o=this.getOptions();return z()(n,Jn(this.publicId,n,e,o)),n.srcset||(n.srcset=Wn(this.publicId,o)),!n.media&&o.media&&(n.media=function(e){var t=[];return null!=e&&(null!=e.min_width&&t.push("(min-width: ".concat(e.min_width,"px)")),null!=e.max_width&&t.push("(max-width: ".concat(e.max_width,"px)"))),t.join(" and ")}(o.media)),n}}])&&ho(n.prototype,o),r&&ho(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,r}(Tn);function jo(e){return(jo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function wo(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ao(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Oo(o.key),o)}}function Oo(e){var t=function(e,t){if("object"!=jo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=jo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==jo(t)?t:t+""}function Do(e,t,n){return t=Co(t),function(e,t){if(t&&("object"===jo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],Co(e).constructor):t.apply(e,n))}function Eo(){return(Eo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=Bo(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(this,arguments)}function Bo(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Co(e)););return e}function Co(e){return(Co=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function So(e,t){return(So=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var ko=function(e){function PictureTag(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return wo(this,PictureTag),(t=Do(this,PictureTag,["picture",e,n])).widthList=o,t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&So(e,t)}(PictureTag,e),t=PictureTag,(n=[{key:"content",value:function(){var e=this;return this.widthList.map((function(t){var n=t.min_width,o=t.max_width,r=t.transformation,i=e.getOptions(),s=new Cn(i);return s.chain().fromOptions("string"==typeof r?{raw_transformation:r}:r),(i=Fe(i)).media={min_width:n,max_width:o},i.transformation=s,new go(e.publicId,i).toHtml()})).join("")+new lo(this.publicId,this.getOptions()).toHtml()}},{key:"attributes",value:function(){var e=Eo(Co(PictureTag.prototype),"attributes",this).call(this);return delete e.width,delete e.height,e}},{key:"closeTag",value:function(){return"</"+this.name+">"}}])&&Ao(t.prototype,n),o&&Ao(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(Tn);function xo(e){return(xo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Fo(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Po(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,To(o.key),o)}}function To(e){var t=function(e,t){if("object"!=xo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=xo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==xo(t)?t:t+""}function Io(e,t,n){return t=zo(t),function(e,t){if(t&&("object"===xo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],zo(e).constructor):t.apply(e,n))}function Ro(){return(Ro="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=Lo(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(this,arguments)}function Lo(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=zo(e)););return e}function zo(e){return(zo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Mo(e,t){return(Mo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var No=["source_types","source_transformation","fallback_content","poster","sources"],Vo=["webm","mp4","ogv"],Uo={format:"jpg",resource_type:"video"},Ho=function(e){function VideoTag(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Fo(this,VideoTag),t=ve({},t,ie),Io(this,VideoTag,["video",e.replace(/\.(mp4|ogv|webm)$/,""),t])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mo(e,t)}(VideoTag,e),t=VideoTag,(n=[{key:"setSourceTransformation",value:function(e){return this.transformation().sourceTransformation(e),this}},{key:"setSourceTypes",value:function(e){return this.transformation().sourceTypes(e),this}},{key:"setPoster",value:function(e){return this.transformation().poster(e),this}},{key:"setFallbackContent",value:function(e){return this.transformation().fallbackContent(e),this}},{key:"content",value:function(){var e=this,t=this.transformation().getValue("source_types"),n=this.transformation().getValue("source_transformation"),o=this.transformation().getValue("fallback_content"),r=this.getOption("sources"),i=[];return F()(r)&&!Ie(r)?i=r.map((function(t){var n=Wn(e.publicId,ve({},t.transformations||{},{resource_type:"video",format:t.type}),e.getOptions());return e.createSourceTag(n,t.type,t.codecs)})):(Ie(t)&&(t=Vo),F()(t)&&(i=t.map((function(t){var o=Wn(e.publicId,ve({},n[t]||{},{resource_type:"video",format:t}),e.getOptions());return e.createSourceTag(o,t)})))),i.join("")+o}},{key:"attributes",value:function(){var e=this.getOption("source_types"),t=this.getOption("poster");if(void 0===t&&(t={}),T()(t)){var n=null!=t.public_id?re:Uo;t=Wn(t.public_id||this.publicId,ve({},t,n,this.getOptions()))}var o=Ro(zo(VideoTag.prototype),"attributes",this).call(this)||{};return o=fe(o,No),!Ie(this.getOption("sources"))||Ie(e)||F()(e)||(o.src=Wn(this.publicId,this.getOptions(),{resource_type:"video",format:e})),null!=t&&(o.poster=t),o}},{key:"createSourceTag",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=null;if(!Ie(t)){var r="ogv"===t?"ogg":t;if(o="video/"+r,!Ie(n)){var i=F()(n)?n.join(", "):n;o+="; codecs="+i}}return"<source "+this.htmlAttrs({src:e,type:o})+">"}}])&&Po(t.prototype,n),o&&Po(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(Tn);function Wo(e){return(Wo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function $o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Go(o.key),o)}}function Go(e){var t=function(e,t){if("object"!=Wo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Wo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Wo(t)?t:t+""}function Ko(e,t,n){return t=qo(t),function(e,t){if(t&&("object"===Wo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],qo(e).constructor):t.apply(e,n))}function qo(e){return(qo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Yo(e,t){return(Yo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Qo=function(e){function ClientHintsMetaTag(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,ClientHintsMetaTag),Ko(this,ClientHintsMetaTag,["meta",void 0,v()({"http-equiv":"Accept-CH",content:"DPR, Viewport-Width, Width"},e)])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yo(e,t)}(ClientHintsMetaTag,e),t=ClientHintsMetaTag,(n=[{key:"closeTag",value:function(){return""}}])&&$o(t.prototype,n),o&&$o(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}(Tn);function Zo(e){return function(e){if(Array.isArray(e))return Xo(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Xo(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Xo(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Jo=function(e,t,n,o){return new Promise((function(r,i){e.innerHTML=t.videoTag(n,o).toHtml(),e.querySelector(".cld-transparent-video").style.width="100%",r(e)}))};var er=function(e,t){e.transformation?e.transformation.push({flags:[t]}):(e.flags||(e.flags=[]),"string"==typeof e.flags&&(e.flags=[e.flags]),e.flags.push(t))};var tr=function(e){e.autoplay=!0,e.muted=!0,e.controls=!1,e.max_timeout_ms=e.max_timeout_ms||ee,e.class=e.class||"",e.class+=" cld-transparent-video",e.externalLibraries=e.externalLibraries||{},e.externalLibraries.seeThru||(e.externalLibraries.seeThru=ue.seeThru),er(e,"alpha")};var nr=function(e,t,n){return new Promise((function(o,r){if(n)o();else{var i=document.createElement("script");i.src=e;var s=setTimeout((function(){r({status:"error",message:"Timeout loading script ".concat(e)})}),t);i.onerror=function(){clearTimeout(s),r({status:"error",message:"Error loading ".concat(e)})},i.onload=function(){clearTimeout(s),o()},document.head.appendChild(i)}}))};function or(e){return new Promise((function(t,n){fetch(e).then((function(e){e.blob().then((function(e){t(e)}))})).catch((function(){n("error")}))}))}function rr(e){return new Promise((function(t,n){var o=new XMLHttpRequest;o.responseType="blob",o.onload=function(e){t(o.response)},o.onerror=function(){n("error")},o.open("GET",e,!0),o.send()}))}var ir=function(e,t){return new Promise((function(n,o){var r=function(e,t){return setTimeout((function(){t({status:"error",message:"Timeout loading Blob URL"})}),e)}(t,o);("undefined"!=typeof fetch&&fetch?or:rr)(e).then((function(e){n({status:"success",payload:{blobURL:URL.createObjectURL(e)}})})).catch((function(){o({status:"error",message:"Error loading Blob URL"})})).finally((function(){clearTimeout(r)}))}))};var sr=function(e){var t=e.autoplay,n=e.playsinline,o=e.loop,r=e.muted,i=e.poster,s=e.blobURL,u=e.videoURL,a=document.createElement("video");return a.style.visibility="hidden",a.position="absolute",a.x=0,a.y=0,a.src=s,a.setAttribute("data-video-url",u),t&&a.setAttribute("autoplay",t),n&&a.setAttribute("playsinline",n),o&&a.setAttribute("loop",o),r&&a.setAttribute("muted",r),r&&(a.muted=r),i&&a.setAttribute("poster",i),a.onload=function(){URL.revokeObjectURL(s)},a};var ur=function(e,t,n,o){var r=window,i=r.seeThru,s=r.setTimeout,u=r.clearTimeout;return new Promise((function(r,a){var l=s((function(){a({status:"error",message:"Timeout instantiating seeThru instance"})}),t);if(i)var c=i.create(e).ready((function(){u(l);var e=c.getCanvas();e.style.width="100%",e.className+=" "+n,o&&c.play(),r(c)}));else a({status:"error",message:"Error instantiating seeThru instance"})}))};var ar=function(e,t,n){var o=n.poster,r=n.autoplay,i=n.playsinline,s=n.loop,u=n.muted;return t+=".mp4",new Promise((function(a,l){nr(n.externalLibraries.seeThru,n.max_timeout_ms,window.seeThru).then((function(){ir(t,n.max_timeout_ms).then((function(c){var d=c.payload,f=sr({blobURL:d.blobURL,videoURL:t,poster:o,autoplay:r,playsinline:i,loop:s,muted:u});e.appendChild(f),ur(f,n.max_timeout_ms,n.class,n.autoplay).then((function(){a(e)})).catch((function(e){l(e)}))})).catch((function(e){var t=e.status,n=e.message;l({status:t,message:n})}))})).catch((function(e){var t=e.status,n=e.message;l({status:t,message:n})}))}))};var lr,cr,dr,fr,hr,pr,yr=function(){return new Promise((function(e,t){Ne()&&e(!1);var n=document.createElement("video"),o=n.canPlayType&&n.canPlayType('video/webm; codecs="vp9"');e("maybe"===o||"probably"===o)}))};function mr(e){return(mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _r(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,vr(o.key),o)}}function vr(e){var t=function(e,t){if("object"!=mr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=mr(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mr(t)?t:t+""}dr=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return t*Math.ceil(e/t)},cr=function(e,t){var n;for(n=e.length-2;n>=0&&e[n]>=t;)n--;return e[n+1]},lr=function(e,t,n,o){var r,i,s,u;return!(u=null!=(r=null!=(i=null!=(s=o.responsive_use_breakpoints)?s:o.responsive_use_stoppoints)?i:this.config("responsive_use_breakpoints"))?r:this.config("responsive_use_stoppoints"))||"resize"===u&&!o.resizing?t:this.calc_breakpoint(e,t,n)},fr=function(e){var t,n;for(t=0;(e=null!=e?e.parentNode:void 0)instanceof Element&&!t;)n=window.getComputedStyle(e),/^inline/.test(n.display)||(t=ot(e));return t},pr=function(e,t){return e.replace(/\bdpr_(1\.0|auto)\b/g,"dpr_"+this.device_pixel_ratio(t))},hr=function(e,t){var n;return e>(n=Ve(t,"width")||0)&&(n=e,Ue(t,"width",e)),n};var br=function(){return e=function Cloudinary(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Cloudinary),this.devicePixelRatioCache={},this.responsiveConfig={},this.responsiveResizeInitialized=!1,t=new wt(e),this.config=function(e,n){return t.config(e,n)},this.fromDocument=function(){return t.fromDocument(),this},this.fromEnvironment=function(){return t.fromEnvironment(),this},this.init=function(){return t.init(),this}},n=[{key:"new",value:function(e){return new this(e)}}],(t=[{key:"url",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Wn(e,t,this.config())}},{key:"video_url",value:function(e,t){return t=v()({resource_type:"video"},t),this.url(e,t)}},{key:"video_thumbnail_url",value:function(e,t){return t=v()({},te,t),this.url(e,t)}},{key:"transformation_string",value:function(e){return new Cn(e).serialize()}},{key:"image",value:function(e){var t,n,o,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=this.imageTag(e,r),t=null!=(o=null!=r.client_hints?r.client_hints:this.config("client_hints"))&&o,null!=r.src||t||n.setAttr("src",""),n=n.toDOM(),t||(Ue(n,"src-cache",this.url(e,r)),this.cloudinary_update(n,r)),n}},{key:"imageTag",value:function(e,t){var n;return(n=new lo(e,this.config())).transformation().fromOptions(t),n}},{key:"pictureTag",value:function(e,t,n){var o;return(o=new ko(e,this.config(),n)).transformation().fromOptions(t),o}},{key:"sourceTag",value:function(e,t){var n;return(n=new go(e,this.config())).transformation().fromOptions(t),n}},{key:"video_thumbnail",value:function(e,t){return this.image(e,z()({},te,t))}},{key:"facebook_profile_image",value:function(e,t){return this.image(e,v()({type:"facebook"},t))}},{key:"twitter_profile_image",value:function(e,t){return this.image(e,v()({type:"twitter"},t))}},{key:"twitter_name_profile_image",value:function(e,t){return this.image(e,v()({type:"twitter_name"},t))}},{key:"gravatar_image",value:function(e,t){return this.image(e,v()({type:"gravatar"},t))}},{key:"fetch_image",value:function(e,t){return this.image(e,v()({type:"fetch"},t))}},{key:"video",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.videoTag(e,t).toHtml()}},{key:"videoTag",value:function(e,t){return t=ve({},t,this.config()),new Ho(e,t)}},{key:"sprite_css",value:function(e,t){return t=v()({type:"sprite"},t),e.match(/.css$/)||(t.format="css"),this.url(e,t)}},{key:"responsive",value:function(e){var t,n,o,r,i,s=this,u=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.responsiveConfig=z()(this.responsiveConfig||{},e),r=null!=(t=this.responsiveConfig.responsive_class)?t:this.config("responsive_class"),u&&this.cloudinary_update("img.".concat(r,", img.cld-hidpi"),this.responsiveConfig),(null==(n=null!=(o=this.responsiveConfig.responsive_resize)?o:this.config("responsive_resize"))||n)&&!this.responsiveResizeInitialized){this.responsiveConfig.resizing=this.responsiveResizeInitialized=!0,i=null;var a=function(){var e,t,n,o,u,a;return e=null!=(t=null!=(n=s.responsiveConfig.responsive_debounce)?n:s.config("responsive_debounce"))?t:100,o=function(){i&&(clearTimeout(i),i=null)},u=function(){return s.cloudinary_update("img.".concat(r),s.responsiveConfig)},a=function(){return o(),u()},e?(o(),void(i=setTimeout(a,e))):u()};return window.addEventListener("resize",a),function(){return window.removeEventListener("resize",a)}}}},{key:"calc_breakpoint",value:function(e,t,n){var o=Ve(e,"breakpoints")||Ve(e,"stoppoints")||this.config("breakpoints")||this.config("stoppoints")||dr;return U()(o)?o(t,n):(R()(o)&&(o=o.split(",").map((function(e){return parseInt(e)})).sort((function(e,t){return e-t}))),cr(o,t))}},{key:"calc_stoppoint",value:function(e,t,n){return this.calc_breakpoint(e,t,n)}},{key:"device_pixel_ratio",value:function(e){e=null==e||e;var t=("undefined"!=typeof window&&null!==window?window.devicePixelRatio:void 0)||1;e&&(t=Math.ceil(t)),(t<=0||NaN===t)&&(t=1);var n=t.toString();return n.match(/^\d+$/)&&(n+=".0"),n}},{key:"processImageTags",value:function(e,t){if(Ie(e))return this;t=ve({},t||{},this.config());var n=e.filter((function(e){return/^img$/i.test(e.tagName)})).map((function(e){var n=v()({width:e.getAttribute("width"),height:e.getAttribute("height"),src:e.getAttribute("src")},t),o=n.source||n.src;delete n.source,delete n.src;var r=new Cn(n).toHtmlAttributes();return Ue(e,"src-cache",Wn(o,n)),e.setAttribute("width",r.width),e.setAttribute("height",r.height),e}));return this.cloudinary_update(n,t),this}},{key:"cloudinary_update",value:function(e,t){var n,o,r,i,s=this;if(null===e)return this;null==t&&(t={});var u,a=null!=t.responsive?t.responsive:this.config("responsive");e=function(e){return F()(e)?e:"NodeList"===e.constructor.name?Zo(e):R()(e)?Array.prototype.slice.call(document.querySelectorAll(e),0):[e]}(e),u=this.responsiveConfig&&null!=this.responsiveConfig.responsive_class?this.responsiveConfig.responsive_class:null!=t.responsive_class?t.responsive_class:this.config("responsive_class");var l=null!=t.round_dpr?t.round_dpr:this.config("round_dpr");return e.forEach((function(c){if(/img/i.test(c.tagName)){var d=!0;if(a&&qe(c,u),!Ie(o=Ve(c,"src-cache")||Ve(c,"src"))){o=pr.call(s,o,l),Tn.isResponsive(c,u)&&(0!==(n=fr(c))?(/w_auto:breakpoints/.test(o)?(i=hr(n,c))?o=o.replace(/w_auto:breakpoints([_0-9]*)(:[0-9]+)?/,"w_auto:breakpoints$1:".concat(i)):d=!1:(r=/w_auto(:(\d+))?/.exec(o))&&(i=lr.call(s,c,n,r[2],t),(i=hr(i,c))?o=o.replace(/w_auto[^,\/]*/g,"w_".concat(i)):d=!1),$e(c,"width"),t.responsive_preserve_height||$e(c,"height")):d=!1);var f="lazy"===t.loading&&!s.isNativeLazyLoadSupported()&&s.isLazyLoadSupported()&&!e[0].getAttribute("src");(d||f)&&s.setAttributeIfExists(e[0],"width","data-width"),d&&!f&&We(c,"src",o)}}})),this}},{key:"setAttributeIfExists",value:function(e,t,n){var o=e.getAttribute(n);null!=o&&We(e,t,o)}},{key:"isLazyLoadSupported",value:function(){return window&&"IntersectionObserver"in window}},{key:"isNativeLazyLoadSupported",value:function(){return"loading"in HTMLImageElement.prototype}},{key:"transformation",value:function(e){return Cn.new(this.config()).fromOptions(e).setParent(this)}},{key:"injectTransparentVideoElement",value:function(e,t){var n=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new Promise((function(r,i){e||i({status:"error",message:"Expecting htmlElContainer to be HTMLElement"}),tr(o);var s=n.video_url(t,o);yr().then((function(u){var a;u?(a=Jo(e,n,t,o),r(e)):a=ar(e,s,o),a.then((function(){r(e)})).catch((function(e){var t=e.status,n=e.message;i({status:t,message:n})}))})).catch((function(e){var t=e.status,n=e.message;i({status:t,message:n})}))}))}}])&&_r(e.prototype,t),n&&_r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();v()(br,o);var gr=br;t.default={ClientHintsMetaTag:Qo,Cloudinary:gr,Condition:pt,Configuration:wt,Expression:ut,crc32:s,FetchLayer:Kt,HtmlTag:Tn,ImageTag:lo,Layer:Et,PictureTag:ko,SubtitlesLayer:Nt,TextLayer:Pt,Transformation:Cn,utf8_encode:i,Util:r,VideoTag:Ho}}})}));