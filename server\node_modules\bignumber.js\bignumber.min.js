/* bignumber.js v9.0.0 https://github.com/MikeMcl/bignumber.js/LICENCE */!function(e){"use strict";var r,x=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,L=Math.ceil,U=Math.floor,I="[BigNumber Error] ",T=I+"Number primitive has more than 15 significant digits: ",C=1e14,M=14,G=9007199254740991,k=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],F=1e7,q=1e9;function j(e){var r=0|e;return 0<e||e===r?r:r-1}function $(e){for(var r,n,t=1,i=e.length,o=e[0]+"";t<i;){for(r=e[t++]+"",n=M-r.length;n--;r="0"+r);o+=r}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function z(e,r){var n,t,i=e.c,o=r.c,s=e.s,f=r.s,u=e.e,l=r.e;if(!s||!f)return null;if(n=i&&!i[0],t=o&&!o[0],n||t)return n?t?0:-f:s;if(s!=f)return s;if(n=s<0,t=u==l,!i||!o)return t?0:!i^n?1:-1;if(!t)return l<u^n?1:-1;for(f=(u=i.length)<(l=o.length)?u:l,s=0;s<f;s++)if(i[s]!=o[s])return i[s]>o[s]^n?1:-1;return u==l?0:l<u^n?1:-1}function H(e,r,n,t){if(e<r||n<e||e!==U(e))throw Error(I+(t||"Argument")+("number"==typeof e?e<r||n<e?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function V(e){var r=e.c.length-1;return j(e.e/M)==r&&e.c[r]%2!=0}function W(e,r){return(1<e.length?e.charAt(0)+"."+e.slice(1):e)+(r<0?"e":"e+")+r}function X(e,r,n){var t,i;if(r<0){for(i=n+".";++r;i+=n);e=i+e}else if(++r>(t=e.length)){for(i=n,r-=t;--r;i+=n);e+=i}else r<t&&(e=e.slice(0,r)+"."+e.slice(r));return e}(r=function e(r){var v,a,h,n,l,s,f,u,c,g,t=B.prototype={constructor:B,toString:null,valueOf:null},w=new B(1),N=20,O=4,p=-7,d=21,m=-1e7,y=1e7,b=!1,o=1,E=0,A={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},S="0123456789abcdefghijklmnopqrstuvwxyz";function B(e,r){var n,t,i,o,s,f,u,l,c=this;if(!(c instanceof B))return new B(e,r);if(null==r){if(e&&!0===e._isBigNumber)return c.s=e.s,void(!e.c||e.e>y?c.c=c.e=null:e.e<m?c.c=[c.e=0]:(c.e=e.e,c.c=e.c.slice()));if((f="number"==typeof e)&&0*e==0){if(c.s=1/e<0?(e=-e,-1):1,e===~~e){for(o=0,s=e;10<=s;s/=10,o++);return void(c.c=y<o?c.e=null:(c.e=o,[e]))}l=String(e)}else{if(!x.test(l=String(e)))return h(c,l,f);c.s=45==l.charCodeAt(0)?(l=l.slice(1),-1):1}-1<(o=l.indexOf("."))&&(l=l.replace(".","")),0<(s=l.search(/e/i))?(o<0&&(o=s),o+=+l.slice(s+1),l=l.substring(0,s)):o<0&&(o=l.length)}else{if(H(r,2,S.length,"Base"),10==r)return D(c=new B(e),N+c.e+1,O);if(l=String(e),f="number"==typeof e){if(0*e!=0)return h(c,l,f,r);if(c.s=1/e<0?(l=l.slice(1),-1):1,B.DEBUG&&15<l.replace(/^0\.0*|\./,"").length)throw Error(T+e)}else c.s=45===l.charCodeAt(0)?(l=l.slice(1),-1):1;for(n=S.slice(0,r),o=s=0,u=l.length;s<u;s++)if(n.indexOf(t=l.charAt(s))<0){if("."==t){if(o<s){o=u;continue}}else if(!i&&(l==l.toUpperCase()&&(l=l.toLowerCase())||l==l.toLowerCase()&&(l=l.toUpperCase()))){i=!0,s=-1,o=0;continue}return h(c,String(e),f,r)}f=!1,-1<(o=(l=a(l,r,10,c.s)).indexOf("."))?l=l.replace(".",""):o=l.length}for(s=0;48===l.charCodeAt(s);s++);for(u=l.length;48===l.charCodeAt(--u););if(l=l.slice(s,++u)){if(u-=s,f&&B.DEBUG&&15<u&&(G<e||e!==U(e)))throw Error(T+c.s*e);if((o=o-s-1)>y)c.c=c.e=null;else if(o<m)c.c=[c.e=0];else{if(c.e=o,c.c=[],s=(o+1)%M,o<0&&(s+=M),s<u){for(s&&c.c.push(+l.slice(0,s)),u-=M;s<u;)c.c.push(+l.slice(s,s+=M));s=M-(l=l.slice(s)).length}else s-=u;for(;s--;l+="0");c.c.push(+l)}}else c.c=[c.e=0]}function i(e,r,n,t){var i,o,s,f,u;if(null==n?n=O:H(n,0,8),!e.c)return e.toString();if(i=e.c[0],s=e.e,null==r)u=$(e.c),u=1==t||2==t&&(s<=p||d<=s)?W(u,s):X(u,s,"0");else if(o=(e=D(new B(e),r,n)).e,f=(u=$(e.c)).length,1==t||2==t&&(r<=o||o<=p)){for(;f<r;u+="0",f++);u=W(u,o)}else if(r-=s,u=X(u,o,"0"),f<o+1){if(0<--r)for(u+=".";r--;u+="0");}else if(0<(r+=o-f))for(o+1==f&&(u+=".");r--;u+="0");return e.s<0&&i?"-"+u:u}function R(e,r){for(var n,t=1,i=new B(e[0]);t<e.length;t++){if(!(n=new B(e[t])).s){i=n;break}r.call(i,n)&&(i=n)}return i}function _(e,r,n){for(var t=1,i=r.length;!r[--i];r.pop());for(i=r[0];10<=i;i/=10,t++);return(n=t+n*M-1)>y?e.c=e.e=null:e.c=n<m?[e.e=0]:(e.e=n,r),e}function D(e,r,n,t){var i,o,s,f,u,l,c,a=e.c,h=k;if(a){e:{for(i=1,f=a[0];10<=f;f/=10,i++);if((o=r-i)<0)o+=M,s=r,c=(u=a[l=0])/h[i-s-1]%10|0;else if((l=L((o+1)/M))>=a.length){if(!t)break e;for(;a.length<=l;a.push(0));u=c=0,s=(o%=M)-M+(i=1)}else{for(u=f=a[l],i=1;10<=f;f/=10,i++);c=(s=(o%=M)-M+i)<0?0:u/h[i-s-1]%10|0}if(t=t||r<0||null!=a[l+1]||(s<0?u:u%h[i-s-1]),t=n<4?(c||t)&&(0==n||n==(e.s<0?3:2)):5<c||5==c&&(4==n||t||6==n&&(0<o?0<s?u/h[i-s]:0:a[l-1])%10&1||n==(e.s<0?8:7)),r<1||!a[0])return a.length=0,t?(r-=e.e+1,a[0]=h[(M-r%M)%M],e.e=-r||0):a[0]=e.e=0,e;if(0==o?(a.length=l,f=1,l--):(a.length=l+1,f=h[M-o],a[l]=0<s?U(u/h[i-s]%h[s])*f:0),t)for(;;){if(0==l){for(o=1,s=a[0];10<=s;s/=10,o++);for(s=a[0]+=f,f=1;10<=s;s/=10,f++);o!=f&&(e.e++,a[0]==C&&(a[0]=1));break}if(a[l]+=f,a[l]!=C)break;a[l--]=0,f=1}for(o=a.length;0===a[--o];a.pop());}e.e>y?e.c=e.e=null:e.e<m&&(e.c=[e.e=0])}return e}function P(e){var r,n=e.e;return null===n?e.toString():(r=$(e.c),r=n<=p||d<=n?W(r,n):X(r,n,"0"),e.s<0?"-"+r:r)}return B.clone=e,B.ROUND_UP=0,B.ROUND_DOWN=1,B.ROUND_CEIL=2,B.ROUND_FLOOR=3,B.ROUND_HALF_UP=4,B.ROUND_HALF_DOWN=5,B.ROUND_HALF_EVEN=6,B.ROUND_HALF_CEIL=7,B.ROUND_HALF_FLOOR=8,B.EUCLID=9,B.config=B.set=function(e){var r,n;if(null!=e){if("object"!=typeof e)throw Error(I+"Object expected: "+e);if(e.hasOwnProperty(r="DECIMAL_PLACES")&&(H(n=e[r],0,q,r),N=n),e.hasOwnProperty(r="ROUNDING_MODE")&&(H(n=e[r],0,8,r),O=n),e.hasOwnProperty(r="EXPONENTIAL_AT")&&((n=e[r])&&n.pop?(H(n[0],-q,0,r),H(n[1],0,q,r),p=n[0],d=n[1]):(H(n,-q,q,r),p=-(d=n<0?-n:n))),e.hasOwnProperty(r="RANGE"))if((n=e[r])&&n.pop)H(n[0],-q,-1,r),H(n[1],1,q,r),m=n[0],y=n[1];else{if(H(n,-q,q,r),!n)throw Error(I+r+" cannot be zero: "+n);m=-(y=n<0?-n:n)}if(e.hasOwnProperty(r="CRYPTO")){if((n=e[r])!==!!n)throw Error(I+r+" not true or false: "+n);if(n){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw b=!n,Error(I+"crypto unavailable");b=n}else b=n}if(e.hasOwnProperty(r="MODULO_MODE")&&(H(n=e[r],0,9,r),o=n),e.hasOwnProperty(r="POW_PRECISION")&&(H(n=e[r],0,q,r),E=n),e.hasOwnProperty(r="FORMAT")){if("object"!=typeof(n=e[r]))throw Error(I+r+" not an object: "+n);A=n}if(e.hasOwnProperty(r="ALPHABET")){if("string"!=typeof(n=e[r])||/^.$|[+-.\s]|(.).*\1/.test(n))throw Error(I+r+" invalid: "+n);S=n}}return{DECIMAL_PLACES:N,ROUNDING_MODE:O,EXPONENTIAL_AT:[p,d],RANGE:[m,y],CRYPTO:b,MODULO_MODE:o,POW_PRECISION:E,FORMAT:A,ALPHABET:S}},B.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!B.DEBUG)return!0;var r,n,t=e.c,i=e.e,o=e.s;e:if("[object Array]"=={}.toString.call(t)){if((1===o||-1===o)&&-q<=i&&i<=q&&i===U(i)){if(0===t[0]){if(0===i&&1===t.length)return!0;break e}if((r=(i+1)%M)<1&&(r+=M),String(t[0]).length==r){for(r=0;r<t.length;r++)if((n=t[r])<0||C<=n||n!==U(n))break e;if(0!==n)return!0}}}else if(null===t&&null===i&&(null===o||1===o||-1===o))return!0;throw Error(I+"Invalid BigNumber: "+e)},B.maximum=B.max=function(){return R(arguments,t.lt)},B.minimum=B.min=function(){return R(arguments,t.gt)},B.random=(n=9007199254740992,l=Math.random()*n&2097151?function(){return U(Math.random()*n)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var r,n,t,i,o,s=0,f=[],u=new B(w);if(null==e?e=N:H(e,0,q),i=L(e/M),b)if(crypto.getRandomValues){for(r=crypto.getRandomValues(new Uint32Array(i*=2));s<i;)9e15<=(o=131072*r[s]+(r[s+1]>>>11))?(n=crypto.getRandomValues(new Uint32Array(2)),r[s]=n[0],r[s+1]=n[1]):(f.push(o%1e14),s+=2);s=i/2}else{if(!crypto.randomBytes)throw b=!1,Error(I+"crypto unavailable");for(r=crypto.randomBytes(i*=7);s<i;)9e15<=(o=281474976710656*(31&r[s])+1099511627776*r[s+1]+4294967296*r[s+2]+16777216*r[s+3]+(r[s+4]<<16)+(r[s+5]<<8)+r[s+6])?crypto.randomBytes(7).copy(r,s):(f.push(o%1e14),s+=7);s=i/7}if(!b)for(;s<i;)(o=l())<9e15&&(f[s++]=o%1e14);for(i=f[--s],e%=M,i&&e&&(o=k[M-e],f[s]=U(i/o)*o);0===f[s];f.pop(),s--);if(s<0)f=[t=0];else{for(t=-1;0===f[0];f.splice(0,1),t-=M);for(s=1,o=f[0];10<=o;o/=10,s++);s<M&&(t-=M-s)}return u.e=t,u.c=f,u}),B.sum=function(){for(var e=1,r=arguments,n=new B(r[0]);e<r.length;)n=n.plus(r[e++]);return n},a=function(){var d="0123456789";function m(e,r,n,t){for(var i,o,s=[0],f=0,u=e.length;f<u;){for(o=s.length;o--;s[o]*=r);for(s[0]+=t.indexOf(e.charAt(f++)),i=0;i<s.length;i++)s[i]>n-1&&(null==s[i+1]&&(s[i+1]=0),s[i+1]+=s[i]/n|0,s[i]%=n)}return s.reverse()}return function(e,r,n,t,i){var o,s,f,u,l,c,a,h,g=e.indexOf("."),p=N,w=O;for(0<=g&&(u=E,E=0,e=e.replace(".",""),c=(h=new B(r)).pow(e.length-g),E=u,h.c=m(X($(c.c),c.e,"0"),10,n,d),h.e=h.c.length),f=u=(a=m(e,r,n,i?(o=S,d):(o=d,S))).length;0==a[--u];a.pop());if(!a[0])return o.charAt(0);if(g<0?--f:(c.c=a,c.e=f,c.s=t,a=(c=v(c,h,p,w,n)).c,l=c.r,f=c.e),g=a[s=f+p+1],u=n/2,l=l||s<0||null!=a[s+1],l=w<4?(null!=g||l)&&(0==w||w==(c.s<0?3:2)):u<g||g==u&&(4==w||l||6==w&&1&a[s-1]||w==(c.s<0?8:7)),s<1||!a[0])e=l?X(o.charAt(1),-p,o.charAt(0)):o.charAt(0);else{if(a.length=s,l)for(--n;++a[--s]>n;)a[s]=0,s||(++f,a=[1].concat(a));for(u=a.length;!a[--u];);for(g=0,e="";g<=u;e+=o.charAt(a[g++]));e=X(e,f,o.charAt(0))}return e}}(),v=function(){function S(e,r,n){var t,i,o,s,f=0,u=e.length,l=r%F,c=r/F|0;for(e=e.slice();u--;)f=((i=l*(o=e[u]%F)+(t=c*o+(s=e[u]/F|0)*l)%F*F+f)/n|0)+(t/F|0)+c*s,e[u]=i%n;return f&&(e=[f].concat(e)),e}function R(e,r,n,t){var i,o;if(n!=t)o=t<n?1:-1;else for(i=o=0;i<n;i++)if(e[i]!=r[i]){o=e[i]>r[i]?1:-1;break}return o}function _(e,r,n,t){for(var i=0;n--;)e[n]-=i,i=e[n]<r[n]?1:0,e[n]=i*t+e[n]-r[n];for(;!e[0]&&1<e.length;e.splice(0,1));}return function(e,r,n,t,i){var o,s,f,u,l,c,a,h,g,p,w,d,m,v,N,O,y,b=e.s==r.s?1:-1,E=e.c,A=r.c;if(!(E&&E[0]&&A&&A[0]))return new B(e.s&&r.s&&(E?!A||E[0]!=A[0]:A)?E&&0==E[0]||!A?0*b:b/0:NaN);for(g=(h=new B(b)).c=[],b=n+(s=e.e-r.e)+1,i||(i=C,s=j(e.e/M)-j(r.e/M),b=b/M|0),f=0;A[f]==(E[f]||0);f++);if(A[f]>(E[f]||0)&&s--,b<0)g.push(1),u=!0;else{for(v=E.length,O=A.length,b+=2,1<(l=U(i/(A[f=0]+1)))&&(A=S(A,l,i),E=S(E,l,i),O=A.length,v=E.length),m=O,w=(p=E.slice(0,O)).length;w<O;p[w++]=0);y=A.slice(),y=[0].concat(y),N=A[0],A[1]>=i/2&&N++;do{if(l=0,(o=R(A,p,O,w))<0){if(d=p[0],O!=w&&(d=d*i+(p[1]||0)),1<(l=U(d/N)))for(i<=l&&(l=i-1),a=(c=S(A,l,i)).length,w=p.length;1==R(c,p,a,w);)l--,_(c,O<a?y:A,a,i),a=c.length,o=1;else 0==l&&(o=l=1),a=(c=A.slice()).length;if(a<w&&(c=[0].concat(c)),_(p,c,w,i),w=p.length,-1==o)for(;R(A,p,O,w)<1;)l++,_(p,O<w?y:A,w,i),w=p.length}else 0===o&&(l++,p=[0]);g[f++]=l,p[0]?p[w++]=E[m]||0:(p=[E[m]],w=1)}while((m++<v||null!=p[0])&&b--);u=null!=p[0],g[0]||g.splice(0,1)}if(i==C){for(f=1,b=g[0];10<=b;b/=10,f++);D(h,n+(h.e=f+s*M-1)+1,t,u)}else h.e=s,h.r=+u;return h}}(),s=/^(-?)0([xbo])(?=\w[\w.]*$)/i,f=/^([^.]+)\.$/,u=/^\.([^.]+)$/,c=/^-?(Infinity|NaN)$/,g=/^\s*\+(?=[\w.])|^\s+|\s+$/g,h=function(e,r,n,t){var i,o=n?r:r.replace(g,"");if(c.test(o))e.s=isNaN(o)?null:o<0?-1:1;else{if(!n&&(o=o.replace(s,function(e,r,n){return i="x"==(n=n.toLowerCase())?16:"b"==n?2:8,t&&t!=i?e:r}),t&&(i=t,o=o.replace(f,"$1").replace(u,"0.$1")),r!=o))return new B(o,i);if(B.DEBUG)throw Error(I+"Not a"+(t?" base "+t:"")+" number: "+r);e.s=null}e.c=e.e=null},t.absoluteValue=t.abs=function(){var e=new B(this);return e.s<0&&(e.s=1),e},t.comparedTo=function(e,r){return z(this,new B(e,r))},t.decimalPlaces=t.dp=function(e,r){var n,t,i;if(null!=e)return H(e,0,q),null==r?r=O:H(r,0,8),D(new B(this),e+this.e+1,r);if(!(n=this.c))return null;if(t=((i=n.length-1)-j(this.e/M))*M,i=n[i])for(;i%10==0;i/=10,t--);return t<0&&(t=0),t},t.dividedBy=t.div=function(e,r){return v(this,new B(e,r),N,O)},t.dividedToIntegerBy=t.idiv=function(e,r){return v(this,new B(e,r),0,1)},t.exponentiatedBy=t.pow=function(e,r){var n,t,i,o,s,f,u,l,c=this;if((e=new B(e)).c&&!e.isInteger())throw Error(I+"Exponent not an integer: "+P(e));if(null!=r&&(r=new B(r)),s=14<e.e,!c.c||!c.c[0]||1==c.c[0]&&!c.e&&1==c.c.length||!e.c||!e.c[0])return l=new B(Math.pow(+P(c),s?2-V(e):+P(e))),r?l.mod(r):l;if(f=e.s<0,r){if(r.c?!r.c[0]:!r.s)return new B(NaN);(t=!f&&c.isInteger()&&r.isInteger())&&(c=c.mod(r))}else{if(9<e.e&&(0<c.e||c.e<-1||(0==c.e?1<c.c[0]||s&&24e7<=c.c[1]:c.c[0]<8e13||s&&c.c[0]<=9999975e7)))return o=c.s<0&&V(e)?-0:0,-1<c.e&&(o=1/o),new B(f?1/o:o);E&&(o=L(E/M+2))}for(u=s?(n=new B(.5),f&&(e.s=1),V(e)):(i=Math.abs(+P(e)))%2,l=new B(w);;){if(u){if(!(l=l.times(c)).c)break;o?l.c.length>o&&(l.c.length=o):t&&(l=l.mod(r))}if(i){if(0===(i=U(i/2)))break;u=i%2}else if(D(e=e.times(n),e.e+1,1),14<e.e)u=V(e);else{if(0==(i=+P(e)))break;u=i%2}c=c.times(c),o?c.c&&c.c.length>o&&(c.c.length=o):t&&(c=c.mod(r))}return t?l:(f&&(l=w.div(l)),r?l.mod(r):o?D(l,E,O,void 0):l)},t.integerValue=function(e){var r=new B(this);return null==e?e=O:H(e,0,8),D(r,r.e+1,e)},t.isEqualTo=t.eq=function(e,r){return 0===z(this,new B(e,r))},t.isFinite=function(){return!!this.c},t.isGreaterThan=t.gt=function(e,r){return 0<z(this,new B(e,r))},t.isGreaterThanOrEqualTo=t.gte=function(e,r){return 1===(r=z(this,new B(e,r)))||0===r},t.isInteger=function(){return!!this.c&&j(this.e/M)>this.c.length-2},t.isLessThan=t.lt=function(e,r){return z(this,new B(e,r))<0},t.isLessThanOrEqualTo=t.lte=function(e,r){return-1===(r=z(this,new B(e,r)))||0===r},t.isNaN=function(){return!this.s},t.isNegative=function(){return this.s<0},t.isPositive=function(){return 0<this.s},t.isZero=function(){return!!this.c&&0==this.c[0]},t.minus=function(e,r){var n,t,i,o,s=this,f=s.s;if(r=(e=new B(e,r)).s,!f||!r)return new B(NaN);if(f!=r)return e.s=-r,s.plus(e);var u=s.e/M,l=e.e/M,c=s.c,a=e.c;if(!u||!l){if(!c||!a)return c?(e.s=-r,e):new B(a?s:NaN);if(!c[0]||!a[0])return a[0]?(e.s=-r,e):new B(c[0]?s:3==O?-0:0)}if(u=j(u),l=j(l),c=c.slice(),f=u-l){for((i=(o=f<0)?(f=-f,c):(l=u,a)).reverse(),r=f;r--;i.push(0));i.reverse()}else for(t=(o=(f=c.length)<(r=a.length))?f:r,f=r=0;r<t;r++)if(c[r]!=a[r]){o=c[r]<a[r];break}if(o&&(i=c,c=a,a=i,e.s=-e.s),0<(r=(t=a.length)-(n=c.length)))for(;r--;c[n++]=0);for(r=C-1;f<t;){if(c[--t]<a[t]){for(n=t;n&&!c[--n];c[n]=r);--c[n],c[t]+=C}c[t]-=a[t]}for(;0==c[0];c.splice(0,1),--l);return c[0]?_(e,c,l):(e.s=3==O?-1:1,e.c=[e.e=0],e)},t.modulo=t.mod=function(e,r){var n,t,i=this;return e=new B(e,r),!i.c||!e.s||e.c&&!e.c[0]?new B(NaN):!e.c||i.c&&!i.c[0]?new B(i):(9==o?(t=e.s,e.s=1,n=v(i,e,0,3),e.s=t,n.s*=t):n=v(i,e,0,o),(e=i.minus(n.times(e))).c[0]||1!=o||(e.s=i.s),e)},t.multipliedBy=t.times=function(e,r){var n,t,i,o,s,f,u,l,c,a,h,g,p,w,d,m=this,v=m.c,N=(e=new B(e,r)).c;if(!(v&&N&&v[0]&&N[0]))return!m.s||!e.s||v&&!v[0]&&!N||N&&!N[0]&&!v?e.c=e.e=e.s=null:(e.s*=m.s,v&&N?(e.c=[0],e.e=0):e.c=e.e=null),e;for(t=j(m.e/M)+j(e.e/M),e.s*=m.s,(u=v.length)<(a=N.length)&&(p=v,v=N,N=p,i=u,u=a,a=i),i=u+a,p=[];i--;p.push(0));for(w=C,d=F,i=a;0<=--i;){for(n=0,h=N[i]%d,g=N[i]/d|0,o=i+(s=u);i<o;)n=((l=h*(l=v[--s]%d)+(f=g*l+(c=v[s]/d|0)*h)%d*d+p[o]+n)/w|0)+(f/d|0)+g*c,p[o--]=l%w;p[o]=n}return n?++t:p.splice(0,1),_(e,p,t)},t.negated=function(){var e=new B(this);return e.s=-e.s||null,e},t.plus=function(e,r){var n,t=this,i=t.s;if(r=(e=new B(e,r)).s,!i||!r)return new B(NaN);if(i!=r)return e.s=-r,t.minus(e);var o=t.e/M,s=e.e/M,f=t.c,u=e.c;if(!o||!s){if(!f||!u)return new B(i/0);if(!f[0]||!u[0])return u[0]?e:new B(f[0]?t:0*i)}if(o=j(o),s=j(s),f=f.slice(),i=o-s){for((n=0<i?(s=o,u):(i=-i,f)).reverse();i--;n.push(0));n.reverse()}for((i=f.length)-(r=u.length)<0&&(n=u,u=f,f=n,r=i),i=0;r;)i=(f[--r]=f[r]+u[r]+i)/C|0,f[r]=C===f[r]?0:f[r]%C;return i&&(f=[i].concat(f),++s),_(e,f,s)},t.precision=t.sd=function(e,r){var n,t,i;if(null!=e&&e!==!!e)return H(e,1,q),null==r?r=O:H(r,0,8),D(new B(this),e,r);if(!(n=this.c))return null;if(t=(i=n.length-1)*M+1,i=n[i]){for(;i%10==0;i/=10,t--);for(i=n[0];10<=i;i/=10,t++);}return e&&this.e+1>t&&(t=this.e+1),t},t.shiftedBy=function(e){return H(e,-G,G),this.times("1e"+e)},t.squareRoot=t.sqrt=function(){var e,r,n,t,i,o=this,s=o.c,f=o.s,u=o.e,l=N+4,c=new B("0.5");if(1!==f||!s||!s[0])return new B(!f||f<0&&(!s||s[0])?NaN:s?o:1/0);if((n=0==(f=Math.sqrt(+P(o)))||f==1/0?(((r=$(s)).length+u)%2==0&&(r+="0"),f=Math.sqrt(+r),u=j((u+1)/2)-(u<0||u%2),new B(r=f==1/0?"1e"+u:(r=f.toExponential()).slice(0,r.indexOf("e")+1)+u)):new B(f+"")).c[0])for((f=(u=n.e)+l)<3&&(f=0);;)if(i=n,n=c.times(i.plus(v(o,i,l,1))),$(i.c).slice(0,f)===(r=$(n.c)).slice(0,f)){if(n.e<u&&--f,"9999"!=(r=r.slice(f-3,f+1))&&(t||"4999"!=r)){+r&&(+r.slice(1)||"5"!=r.charAt(0))||(D(n,n.e+N+2,1),e=!n.times(n).eq(o));break}if(!t&&(D(i,i.e+N+2,0),i.times(i).eq(o))){n=i;break}l+=4,f+=4,t=1}return D(n,n.e+N+1,O,e)},t.toExponential=function(e,r){return null!=e&&(H(e,0,q),e++),i(this,e,r,1)},t.toFixed=function(e,r){return null!=e&&(H(e,0,q),e=e+this.e+1),i(this,e,r)},t.toFormat=function(e,r,n){var t;if(null==n)null!=e&&r&&"object"==typeof r?(n=r,r=null):e&&"object"==typeof e?(n=e,e=r=null):n=A;else if("object"!=typeof n)throw Error(I+"Argument not an object: "+n);if(t=this.toFixed(e,r),this.c){var i,o=t.split("."),s=+n.groupSize,f=+n.secondaryGroupSize,u=n.groupSeparator||"",l=o[0],c=o[1],a=this.s<0,h=a?l.slice(1):l,g=h.length;if(f&&(i=s,s=f,g-=f=i),0<s&&0<g){for(i=g%s||s,l=h.substr(0,i);i<g;i+=s)l+=u+h.substr(i,s);0<f&&(l+=u+h.slice(i)),a&&(l="-"+l)}t=c?l+(n.decimalSeparator||"")+((f=+n.fractionGroupSize)?c.replace(new RegExp("\\d{"+f+"}\\B","g"),"$&"+(n.fractionGroupSeparator||"")):c):l}return(n.prefix||"")+t+(n.suffix||"")},t.toFraction=function(e){var r,n,t,i,o,s,f,u,l,c,a,h,g=this,p=g.c;if(null!=e&&(!(f=new B(e)).isInteger()&&(f.c||1!==f.s)||f.lt(w)))throw Error(I+"Argument "+(f.isInteger()?"out of range: ":"not an integer: ")+P(f));if(!p)return new B(g);for(r=new B(w),l=n=new B(w),t=u=new B(w),h=$(p),o=r.e=h.length-g.e-1,r.c[0]=k[(s=o%M)<0?M+s:s],e=!e||0<f.comparedTo(r)?0<o?r:l:f,s=y,y=1/0,f=new B(h),u.c[0]=0;c=v(f,r,0,1),1!=(i=n.plus(c.times(t))).comparedTo(e);)n=t,t=i,l=u.plus(c.times(i=l)),u=i,r=f.minus(c.times(i=r)),f=i;return i=v(e.minus(n),t,0,1),u=u.plus(i.times(l)),n=n.plus(i.times(t)),u.s=l.s=g.s,a=v(l,t,o*=2,O).minus(g).abs().comparedTo(v(u,n,o,O).minus(g).abs())<1?[l,t]:[u,n],y=s,a},t.toNumber=function(){return+P(this)},t.toPrecision=function(e,r){return null!=e&&H(e,1,q),i(this,e,r,2)},t.toString=function(e){var r,n=this,t=n.s,i=n.e;return null===i?t?(r="Infinity",t<0&&(r="-"+r)):r="NaN":(r=null==e?i<=p||d<=i?W($(n.c),i):X($(n.c),i,"0"):10===e?X($((n=D(new B(n),N+i+1,O)).c),n.e,"0"):(H(e,2,S.length,"Base"),a(X($(n.c),i,"0"),10,e,t,!0)),t<0&&n.c[0]&&(r="-"+r)),r},t.valueOf=t.toJSON=function(){return P(this)},t._isBigNumber=!0,null!=r&&B.set(r),B}()).default=r.BigNumber=r,"function"==typeof define&&define.amd?define(function(){return r}):"undefined"!=typeof module&&module.exports?module.exports=r:(e||(e="undefined"!=typeof self&&self?self:window),e.BigNumber=r)}(this);